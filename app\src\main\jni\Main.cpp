#include <list>
#include <vector>
#include <string.h>
#include <pthread.h>
#include <thread>
#include <cstring>
#include <jni.h>
#include <unistd.h>
#include <fstream>
#include <iostream>
#include <dlfcn.h>
#include "Includes/Logger.h"
#include "Includes/obfuscate.h"
#include "Includes/Utils.h"
#include "KittyMemory/MemoryPatch.h"
#include "Menu/Setup.h"
#include "TranHuong/Tools.h"
#include "TranHuong/fake_dlfcn.h"
#include "TranHuong/Il2Cpp.h"
//Target lib here
#define targetLibName OBFUSCATE("libil2cpp.so")
#define MOD_LIBNAME OBFUSCATE("libESPVN.so")
#define GAME_CODE "aovvn"
#define MOD_VERSION "VN-ESP"
#define PhienBan ("Mod ESP VN")
#include "Includes/Macros.h"
#include <sys/stat.h>

#include "AES/AES.cpp"

// ==================== AUTO FUNCTIONS NOW IN DrawESP ====================
// Auto functions are now called directly in DrawESP via DrawESPAutoLogic()
// No need for forward declarations here anymore





#include "ARMPatch.cpp"
#include "StrEnc.h"

#include <curl/curl.h>
#include "json.hpp"

#include <openssl/evp.h>
#include <openssl/pem.h>
#include <openssl/rsa.h>
#include <openssl/err.h>
#include <openssl/md5.h>
using json = nlohmann::ordered_json;
using namespace std;


#include <fstream>

#include <EGL/egl.h>
#include <GLES3/gl3.h>

#include "LQM/Call_Me.h"

// AimEntityInfo struct is defined in Hooker.h

// ===== ANTICHEAT BYPASS INTEGRATION =====
#include "And64InlineHook/And64InlineHook.hpp"
#include <atomic>
#include <mutex>
#include <thread>
#include <chrono>
#include <random>
#include <algorithm>

// AntiCheat Bypass Class
class AntiCheatBypass {
public:
    static bool Initialize();
    static void Cleanup();
    static void StartAutoBypass();

private:
    // Static member variables
    static std::atomic<bool> initialized;
    static std::atomic<bool> bypass_enabled;
    static std::atomic<int> call_counter;
    static std::atomic<bool> should_stop_monitoring;
    static void* libanogs_base;
    static std::mutex init_mutex;
    static std::thread monitor_thread;

    // Original function pointers
    static int (*orig_AnoSDKOnRecvSignature)(unsigned char*, long, unsigned int, int);
    static long (*orig_loc_1424A8)(long, long, long, long, long);
    static int (*orig_sub_14B058)(unsigned char*, long, unsigned int, int);

    // Hook functions
    static bool HookAnoSDKFunctions();
    static bool HookCoreVerification();

    // Hook implementations
    static int AnoSDKOnRecvSignature_Hook(unsigned char* a1, long a2, unsigned int a3, int a4);
    static long loc_1424A8_Hook(long a1, long a2, long a3, long a4, long a5);
    static int sub_14B058_Hook(unsigned char* a1, long a2, unsigned int a3, int a4);

    // Helper functions
    static bool shouldBypass();
    static void* findLibraryBase(const char* library_name);
    static bool isValidAddress(void* addr);
    static bool verifyFunctionSignature(void* addr, const char* expected_pattern);
    static void obfuscateHookData();
    static void monitorLibraryLoading();
    static bool waitForLibrary(const char* library_name, int max_wait_seconds);
};

// Auto-initialization class
class AutoAntiCheatBypass {
public:
    AutoAntiCheatBypass();
    ~AutoAntiCheatBypass();
};

struct My_Patches { MemoryPatch HackMap;
} hexPatches;

#include "IconManager/IconManager.h"


/////---OFFSETS ONLINE---////

const char *g_key = OBFUSCATE("094412704612345689");
const char *g_iv = OBFUSCATE("0123456789012345");


string EncryptionAES(const string& strSrc)
{
    size_t length = strSrc.length();
    int block_num = length / BLOCK_SIZE + 1;
    char* szDataIn = new char[block_num * BLOCK_SIZE + 1];
    memset(szDataIn, 0x00, block_num * BLOCK_SIZE + 1);
    strcpy(szDataIn, strSrc.c_str());

    int k = length % BLOCK_SIZE;
    int j = length / BLOCK_SIZE;
    int padding = BLOCK_SIZE - k;
    for (int i = 0; i < padding; i++)
    {
        szDataIn[j * BLOCK_SIZE + k + i] = padding;
    }
    szDataIn[block_num * BLOCK_SIZE] = '\0';

    char *szDataOut = new char[block_num * BLOCK_SIZE + 1];
    memset(szDataOut, 0, block_num * BLOCK_SIZE + 1);

    AES aes;
    aes.MakeKey(g_key, g_iv, 16, 16);
    aes.Encrypt(szDataIn, szDataOut, block_num * BLOCK_SIZE, AES::CBC);
    string str = base64_encode((unsigned char*) szDataOut,
                               block_num * BLOCK_SIZE);
    delete[] szDataIn;
    delete[] szDataOut;
    return str;
}


string DecryptionAES(const string& strSrc)
{
    string strData = base64_decode(strSrc);
    size_t length = strData.length();
    char *szDataIn = new char[length + 1];
    memcpy(szDataIn, strData.c_str(), length+1);
    char *szDataOut = new char[length + 1];
    memcpy(szDataOut, strData.c_str(), length+1);

    AES aes;
    aes.MakeKey(g_key, g_iv, 16, 16);
    aes.Decrypt(szDataIn, szDataOut, length, AES::CBC);

    if (0x00 < szDataOut[length - 1] <= 0x16)
    {
        int tmp = szDataOut[length - 1];
        for (int i = length - 1; i >= length - tmp; i--)
        {
            if (szDataOut[i] != tmp)
            {
                memset(szDataOut, 0, length);
                cout << "q" << endl;
                break;
            }
            else
                szDataOut[i] = 0;
        }
    }
    string strDest(szDataOut);
    delete[] szDataIn;
    delete[] szDataOut;
    return strDest;
}



std::string offset = "";
void OffsetsOnline() {
    std::string errMsg;

    struct MemoryStruct chunk{};
    chunk.memory = (char *) malloc(1);
    chunk.size = 0;

    CURL *curl;
    CURLcode res;
    curl = curl_easy_init();
    if (curl) {
        curl_easy_setopt(curl, CURLOPT_CUSTOMREQUEST, "POST");
        curl_easy_setopt(curl, CURLOPT_URL, std::string(OBFUSCATE("https://hmod.io.vn/public/files/Offsets-ESP.php")).c_str());
        curl_easy_setopt(curl, CURLOPT_FOLLOWLOCATION, 1L);
        curl_easy_setopt(curl, CURLOPT_DEFAULT_PROTOCOL, "https");    
        struct curl_slist *headers = NULL;
        headers = curl_slist_append(headers, "Content-Type: application/x-www-form-urlencoded");
        curl_easy_setopt(curl, CURLOPT_HTTPHEADER, headers);

        char data[4096];
        sprintf(data, "game=lqm&nhin_thay_chu_nay=%s","1 đàn chó =)) ");
        curl_easy_setopt(curl, CURLOPT_POSTFIELDS, data);

        curl_easy_setopt(curl, CURLOPT_WRITEFUNCTION, WriteMemoryCallback);
        curl_easy_setopt(curl, CURLOPT_WRITEDATA, (void *) &chunk);

        curl_easy_setopt(curl, CURLOPT_SSL_VERIFYPEER, 0L);
        curl_easy_setopt(curl, CURLOPT_SSL_VERIFYHOST, 0L);

        res = curl_easy_perform(curl);
        
        if (res == CURLE_OK) {
            offset = DecryptionAES(base64_decode(std::string(chunk.memory)));
        } else {
            errMsg = curl_easy_strerror(res);
        }
    }
    curl_easy_cleanup(curl);
}





std::string RandomString(const int len);
std::string CalcMD5(std::string s);
std::string CalcSHA256(std::string s);
std::string RandomString(const int len) {
    static const char alphanumerics[] = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz";
    srand((unsigned) time(0) * getpid());

    std::string tmp;
    tmp.reserve(len);
    for (int i = 0; i < len; ++i) {
        tmp += alphanumerics[rand() % (sizeof(alphanumerics) - 1)];
    }
    return tmp;
}

std::string CalcMD5(std::string s) {
    std::string result;

    unsigned char hash[MD5_DIGEST_LENGTH];
    char tmp[4];

    MD5_CTX md5;
    MD5_Init(&md5);
    MD5_Update(&md5, s.c_str(), s.length());
    MD5_Final(hash, &md5);
    for (unsigned char i : hash) {
        sprintf(tmp, "%02x", i);
        result += tmp;
    }
    return result;
}




/// HACK MAP ---///
bool HackMap = false;

// AIM SYSTEM GLOBAL VARIABLES
AimEntityInfo AimEnemyTarget;
bool AimElsu = false;
bool aimIsCharging = false;
bool aimIgnoreInvisible = true; // NEW: Option to ignore invisible enemies (default: true for safety)
int aimMode = 0, aimType = 1, aimDrawType = 2, aimSkillSlot = 0;

// ENHANCED AIM SYSTEM CONFIGURATION VARIABLES
bool aimUseESPData = true;      // Use ESP data for more accurate targeting
bool aimPredictMovement = true;  // Predict enemy movement
float aimSmoothness = 1.0f;     // Aim smoothness factor (0.1-2.0)
bool aimShowTargetLine = true;   // Show line to current aim target
float aimLineThickness = 2.0f;   // Thickness of aim line (1.0-5.0)

static bool isDumping = false;

EGLBoolean (*orig_eglSwapBuffers)(EGLDisplay dpy, EGLSurface surface);
EGLBoolean _eglSwapBuffers(EGLDisplay dpy, EGLSurface surface) {
    
    eglQuerySurface(dpy, surface, EGL_WIDTH, &glWidth);
    eglQuerySurface(dpy, surface, EGL_HEIGHT, &glHeight);
    
    if (glWidth <= 0 || glHeight <= 0) {
        return eglSwapBuffers(dpy, surface);
    }
    
    if (!Config.InitImGui.initImGui) {
        ImGui::CreateContext();
        
        ImGui_ImplAndroid_Init();
        ImGui_ImplOpenGL3_Init(OBFUSCATE("#version 300 es"));
        
        ImGuiIO* io = &ImGui::GetIO();
        
        io->ConfigWindowsMoveFromTitleBarOnly = true;
        io->IniFilename = NULL;
        
        static const ImWchar icons_ranges[] = {0x0020, 0x00FF, 0x3000, 0x30FF, 0x31F0, 0x31FF, 0xFF00, 0xFFEF, 0x4e00, 0x9FAF, 0};
        ImFontConfig icons_config;

        ImFontConfig CustomFont;
        CustomFont.FontDataOwnedByAtlas = false;

        icons_config.MergeMode = true;
        icons_config.PixelSnapH = true;
        icons_config.OversampleH = 2.5;
        icons_config.OversampleV = 2.5;

        io->Fonts->AddFontFromMemoryTTF((void *)PIRO_data, PIRO_size, 30.0f, NULL, io->Fonts->GetGlyphRangesVietnamese());
        


        io->Fonts->AddFontFromMemoryTTF(const_cast<std::uint8_t*>(Custom), sizeof(Custom), 21.f, &CustomFont);
        io->Fonts->AddFontFromMemoryCompressedTTF(font_awesome_data, font_awesome_size, 19.0f, &icons_config, icons_ranges);
        
        ImFontConfig font_cfg;
        font_cfg.SizePixels = 22.0f;
        io->Fonts->AddFontDefault(&font_cfg);
        
        Config.InitImGui.initImGui = true;
    }
    
    ImGui_ImplOpenGL3_NewFrame();
    ImGui_ImplAndroid_NewFrame(glWidth, glHeight);
    ImGui::NewFrame();

    // ==================== FIXED: Auto functions now called in DrawESP ====================
    // Auto logic is now called directly in DrawESP via DrawESPAutoLogic()
    // exactly like LGL GOC does in DrawESPaim() - this ensures proper timing and data sync

    DrawESP(ImGui::GetForegroundDrawList());

    ImGui::End();
    ImGui::Render();
    ImGui_ImplOpenGL3_RenderDrawData(ImGui::GetDrawData());
    
    
    return orig_eglSwapBuffers(dpy, surface);
}
uintptr_t G_IL2CPP;

void *hack_thread(void *) {
    InitializeIcons();
  espManager = new ESPManager();
    ActorLinker_enemy = new ESPManager();
    while (!il2cppMap) {
        il2cppMap = Tools::GetBaseAddress("libil2cpp.so");
        sleep(1);
    }
    IL2Cpp::Il2CppAttach();
    
    int (*_screenHeight)() = (int (*)()) IL2Cpp::Il2CppGetMethodOffset("UnityEngine.CoreModule.dll","UnityEngine", "Screen", "get_systemHeight",0);
        int (*_screenWidth)() = (int (*)()) IL2Cpp::Il2CppGetMethodOffset("UnityEngine.CoreModule.dll","UnityEngine", "Screen", "get_systemWidth", 0);
    
    if(_screenHeight && _screenWidth){
            screenHeight = _screenHeight();
        
            screenWidth = _screenWidth();
        }
 
      
        
     OffsetsOnline();
    
    std::istringstream iss(offset); 
    std::string line;
    while (std::getline(iss, line)) {
        size_t pos1 = line.find(":");
        size_t pos2 = line.find(":", pos1 + 1);
        size_t pos3 = line.find(":", pos2 + 1);
        size_t pos4 = line.find(":", pos3 + 1);
        size_t pos5 = line.find(":", pos4 + 1);
        size_t pos6 = line.find(":", pos5 + 1);
        size_t pos7 = line.find(":", pos6 + 1);

        std::string str1 = line.substr(0, pos1);
        std::string str2 = line.substr(pos1 + 1, pos2 - pos1 - 1);
        std::string str3 = line.substr(pos2 + 1, pos3 - pos2 - 1);
        std::string str4 = line.substr(pos3 + 1, pos4 - pos3 - 1);
        std::string str5 = line.substr(pos4 + 1, pos5 - pos4 - 1);
        std::string str6 = line.substr(pos5 + 1, pos6 - pos5 - 1);
        std::string str7 = line.substr(pos6 + 1, pos7 - pos6 - 1);
        std::string str8 = line.substr(pos7 + 1);
  
        if (str1 == "PatchIl2cpp") {
            MemoryPatch::createWithHex((uintptr_t)IL2Cpp::Il2CppGetMethodOffset(str2.c_str(), str3.c_str(), str4.c_str(), str5.c_str(), std::stoi(str6)), str7.c_str()).Modify();
        } else if (str1 == "PatchAnogs") {
            MemoryPatch::createWithHex("libanogs.so" , string2Offset(str2.c_str()), str3.c_str()).Modify();
        }
    }
           
        

 //   MemoryPatch::createWithHex("libanogs.so", string2Offset(OBFUSCATE("0x1340DC")),  OBFUSCATE("C0035FD6")).Modify();        
 //  MemoryPatch::createWithHex((uintptr_t)IL2Cpp::Il2CppGetMethodOffset(OBFUSCATE("Project_d.dll"), OBFUSCATE("Assets.Scripts.GameSystem"), OBFUSCATE("CLoginSystem") , OBFUSCATE("_LoginSuccess"), 1),"****************"); 
          
                
     Tools::Hook((void *) DobbySymbolResolver(OBFUSCATE("/system/lib/libandroid.so"), OBFUSCATE("ANativeWindow_getWidth")), (void *) _ANativeWindow_getWidth, (void **) &orig_ANativeWindow_getWidth);
    Tools::Hook((void *) DobbySymbolResolver(OBFUSCATE("/system/lib/libandroid.so"), OBFUSCATE("ANativeWindow_getHeight")), (void *) _ANativeWindow_getHeight, (void **) &orig_ANativeWindow_getHeight);    
    Tools::Hook((void *) DobbySymbolResolver(OBFUSCATE("/system/lib/libEGL.so"), OBFUSCATE("eglSwapBuffers")), (void *) _eglSwapBuffers, (void **) &orig_eglSwapBuffers);
   
AsHero = (void *(*)(void *)) IL2Cpp::Il2CppGetMethodOffset(OBFUSCATE("Project_d.dll"), OBFUSCATE("Kyrios.Actor"), OBFUSCATE("ActorLinker") , OBFUSCATE("AsHero"), 0);
    
    GetHero_Icon = (String* (*)(void *, bool))IL2Cpp::Il2CppGetMethodOffset("Project_d.dll","Assets.Scripts.GameSystem","KillNotifyUT","GetHero_Icon",2);
    
    Tools::Hook((void *) (uintptr_t)IL2Cpp::Il2CppGetMethodOffset(OBFUSCATE("Project_d.dll"), OBFUSCATE("Kyrios.Actor"), OBFUSCATE("ActorLinker") , OBFUSCATE("LateUpdate"), 0), (void *) ActorLinker_Update, (void **) &old_ActorLinker_Update);
    
    Tools::Hook((void *) (uintptr_t)IL2Cpp::Il2CppGetMethodOffset(OBFUSCATE("Project.Plugins_d.dll"), OBFUSCATE("NucleusDrive.Logic"), OBFUSCATE("LActorRoot") , OBFUSCATE("UpdateLogic"), 1), (void *) LActorRoot_UpdateLogic, (void **) &old_LActorRoot_UpdateLogic);
    
    Tools::Hook((void *) (uintptr_t)IL2Cpp::Il2CppGetMethodOffset(OBFUSCATE("Project_d.dll"), OBFUSCATE("Kyrios.Actor"), OBFUSCATE("ActorLinker") , OBFUSCATE("DestroyActor"), 0), (void *) ActorLinker_ActorDestroy, (void **) &old_ActorLinker_ActorDestroy);
    
    Tools::Hook((void *) (uintptr_t)IL2Cpp::Il2CppGetMethodOffset(OBFUSCATE("Project.Plugins_d.dll"), OBFUSCATE("NucleusDrive.Logic"), OBFUSCATE("LActorRoot") , OBFUSCATE("DestroyActor"), 1), (void *) LActorRoot_ActorDestroy, (void **) &old_LActorRoot_ActorDestroy);
    
    
   _SetPlayerName = (void (*)(...))IL2Cpp::Il2CppGetMethodOffset(OBFUSCATE("Project_d.dll"), OBFUSCATE("Assets.Scripts.GameLogic"), OBFUSCATE("HudComponent3D") , OBFUSCATE("SetPlayerName"), 4);

    
    ActorLinker_IsHostPlayer = (bool (*)(void *)) IL2Cpp::Il2CppGetMethodOffset(OBFUSCATE("Project_d.dll"), OBFUSCATE("Kyrios.Actor"), OBFUSCATE("ActorLinker") , OBFUSCATE("IsHostPlayer"), 0);
    ActorLinker_IsHostCamp = (bool (*)(void *)) IL2Cpp::Il2CppGetMethodOffset(OBFUSCATE("Project_d.dll"), OBFUSCATE("Kyrios.Actor"), OBFUSCATE("ActorLinker") , OBFUSCATE("IsHostCamp"), 0);
    ActorLinker_ActorTypeDef = (int (*)(void *)) IL2Cpp::Il2CppGetMethodOffset(OBFUSCATE("Project_d.dll"), OBFUSCATE("Kyrios.Actor"), OBFUSCATE("ActorLinker") , OBFUSCATE("get_objType"), 0);
    ActorLinker_COM_PLAYERCAMP = (int (*)(void *)) IL2Cpp::Il2CppGetMethodOffset(OBFUSCATE("Project_d.dll"), OBFUSCATE("Kyrios.Actor"), OBFUSCATE("ActorLinker") , OBFUSCATE("get_objCamp"), 0);
    ActorLinker_getPosition = (Vector3 (*)(void *)) IL2Cpp::Il2CppGetMethodOffset(OBFUSCATE("Project_d.dll"), OBFUSCATE("Kyrios.Actor"), OBFUSCATE("ActorLinker") , OBFUSCATE("get_position"), 0);
    ActorLinker_get_HPBarVisible = (bool (*)(void *)) IL2Cpp::Il2CppGetMethodOffset(OBFUSCATE("Project_d.dll"), OBFUSCATE("Kyrios.Actor"), OBFUSCATE("ActorLinker") , OBFUSCATE("get_HPBarVisible"), 0);
    ActorLinker_get_ObjID = (int (*)(void *)) IL2Cpp::Il2CppGetMethodOffset(OBFUSCATE("Project_d.dll"), OBFUSCATE("Kyrios.Actor"), OBFUSCATE("ActorLinker") , OBFUSCATE("get_ObjID"), 0);
    ActorLinker_get_bVisible = (bool (*)(void *)) IL2Cpp::Il2CppGetMethodOffset(OBFUSCATE("Project_d.dll"), OBFUSCATE("Kyrios.Actor"), OBFUSCATE("ActorLinker") , OBFUSCATE("get_bVisible"), 0);

    LActorRoot_get_forward = (VInt3 (*)(void *)) IL2Cpp::Il2CppGetMethodOffset(OBFUSCATE("Project.Plugins_d.dll"), OBFUSCATE("NucleusDrive.Logic"), OBFUSCATE("LActorRoot") , OBFUSCATE("get_forward"), 0);
    LActorRoot_get_location = (VInt3 (*)(void *)) IL2Cpp::Il2CppGetMethodOffset(OBFUSCATE("Project.Plugins_d.dll"), OBFUSCATE("NucleusDrive.Logic"), OBFUSCATE("LActorRoot") , OBFUSCATE("get_location"), 0);
    LActorRoot_LHeroWrapper = (uintptr_t (*)(void *)) IL2Cpp::Il2CppGetMethodOffset(OBFUSCATE("Project.Plugins_d.dll"), OBFUSCATE("NucleusDrive.Logic"), OBFUSCATE("LActorRoot") , OBFUSCATE("AsHero"), 0);
    LActorRoot_COM_PLAYERCAMP = (int (*)(void *)) IL2Cpp::Il2CppGetMethodOffset(OBFUSCATE("Project.Plugins_d.dll"), OBFUSCATE("NucleusDrive.Logic"), OBFUSCATE("LActorRoot") , OBFUSCATE("GiveMyEnemyCamp"), 0);
    LActorRoot_get_bActive = (bool (*)(void *)) IL2Cpp::Il2CppGetMethodOffset(OBFUSCATE("Project.Plugins_d.dll"), OBFUSCATE("NucleusDrive.Logic"), OBFUSCATE("LActorRoot") , OBFUSCATE("get_bActive"), 0);
   LActorRoot_get_ObjID = (int (*)(void *)) IL2Cpp::Il2CppGetMethodOffset(OBFUSCATE("Project.Plugins_d.dll"), OBFUSCATE("NucleusDrive.Logic"), OBFUSCATE("LActorRoot") , OBFUSCATE("get_ObjID"), 0);

    GetHeroName = (String *(*)(int)) IL2Cpp::Il2CppGetMethodOffset(OBFUSCATE("Project_d.dll"), OBFUSCATE("Assets.Scripts.GameSystem"), OBFUSCATE("CHeroInfo") , OBFUSCATE("GetHeroName"), 1);
    

    LObjWrapper_get_IsDeadState = (bool (*)(void *)) IL2Cpp::Il2CppGetMethodOffset(OBFUSCATE("Project.Plugins_d.dll"), OBFUSCATE("NucleusDrive.Logic"), OBFUSCATE("LObjWrapper") , OBFUSCATE("get_IsDeadState"), 0);

    ValuePropertyComponent_get_actorHp = (int (*)(void *)) IL2Cpp::Il2CppGetMethodOffset(OBFUSCATE("Project.Plugins_d.dll"), OBFUSCATE("NucleusDrive.Logic"), OBFUSCATE("ValuePropertyComponent") , OBFUSCATE("get_actorHp"), 0);
    ValuePropertyComponent_get_actorHpTotal = (int (*)(void *)) IL2Cpp::Il2CppGetMethodOffset(OBFUSCATE("Project.Plugins_d.dll"), OBFUSCATE("NucleusDrive.Logic"), OBFUSCATE("ValuePropertyComponent") , OBFUSCATE("get_actorHpTotal"), 0);
    
    
    
    OnCameraHeightChanged = (void(*)(void *))IL2Cpp::Il2CppGetMethodOffset(OBFUSCATE("Project_d.dll"), OBFUSCATE(""), OBFUSCATE("CameraSystem") , OBFUSCATE("OnCameraHeightChanged"), 0);
    
    Tools::Hook((void *) (uintptr_t)IL2Cpp::Il2CppGetMethodOffset(OBFUSCATE("Project_d.dll"), OBFUSCATE(""), OBFUSCATE("CameraSystem") , OBFUSCATE("Update"), 0), (void *) CameraSystemUpdate, (void **) &old_CameraSystemUpdate);

    Tools::Hook((void *) (uintptr_t)IL2Cpp::Il2CppGetMethodOffset(OBFUSCATE("Project_d.dll"), OBFUSCATE(""), OBFUSCATE("CameraSystem") , OBFUSCATE("GetCameraHeightRateValue"), 1), (void *) GetCameraHeightRateValue, (void **) &old_GetCameraHeightRateValue);
   
  
   /* 
               /////////FPS CAO///////////
Tools::Hook((void *) (uintptr_t)IL2Cpp::Il2CppGetMethodOffset(OBFUSCATE("Project_d.dll"), OBFUSCATE("Assets.Scripts.Framework"), OBFUSCATE("GameSettings") , OBFUSCATE("get_SupportedBoth60FPS_CameraHeight"), 0), (void *) get_Supported60FPSMode, (void **) &old_get_Supported60FPSMode);


Tools::Hook((void *) (uintptr_t)IL2Cpp::Il2CppGetMethodOffset(OBFUSCATE("Project_d.dll"), OBFUSCATE("Assets.Scripts.Framework"), OBFUSCATE("GameSettings") , OBFUSCATE("get_SupportedBoth60FPS_CameraHeight"), 0), (void *) get_Supported60FPSMode, (void **) &old_get_Supported60FPSMode);
Tools::Hook((void *) (uintptr_t)IL2Cpp::Il2CppGetMethodOffset(OBFUSCATE("Project_d.dll"), OBFUSCATE("Assets.Scripts.Framework"), OBFUSCATE("GameSettings") , OBFUSCATE("get_Supported90FPSMode"), 0), (void *) get_Supported60FPSMode, (void **) &old_get_Supported60FPSMode);
*/
Tools::Hook((void *) (uintptr_t)IL2Cpp::Il2CppGetMethodOffset(OBFUSCATE("Project_d.dll"), OBFUSCATE("Assets.Scripts.Framework"), OBFUSCATE("GameSettings") , OBFUSCATE("get_Supported120FPSMode"), 0), (void *) get_Supported60FPSMode, (void **) &old_get_Supported60FPSMode);

    // AIM SYSTEM HOOKS
    Tools::Hook((void *) (uintptr_t)IL2Cpp::Il2CppGetMethodOffset(OBFUSCATE("Project_d.dll"), OBFUSCATE("Assets.Scripts.GameLogic"), OBFUSCATE("SkillControlIndicator") , OBFUSCATE("GetUseSkillDirection"), 1), (void *) GetUseSkillDirection, (void **) &_GetUseSkillDirection);

   Tools::Hook((void *) (uintptr_t)IL2Cpp::Il2CppGetMethodOffset(OBFUSCATE("Project_d.dll"), OBFUSCATE("Assets.Scripts.GameSystem"), OBFUSCATE("CSkillButtonManager") , OBFUSCATE("UpdateLogic"), 1), (void *) AimUpdateLogic, (void **) &_AimUpdateLogic);

  
   
   //auto bổ trợ + bộc phá//
  Tools::Hook((void *) (uint64_t)IL2Cpp::Il2CppGetMethodOffset(OBFUSCATE("Project_d.dll"), OBFUSCATE("Kyrios.Actor"), OBFUSCATE("ActorLinker") , OBFUSCATE("Update"), 0), (void *) AUpdate, (void **) &old_Update);

   Tools::Hook((void *) (uintptr_t)IL2Cpp::Il2CppGetMethodOffset(OBFUSCATE("Project_d.dll"), OBFUSCATE("Assets.Scripts.GameLogic"), OBFUSCATE("SkillSlot") , OBFUSCATE("LateUpdate"), 1), (void *) Skslot, (void **) &_Skslot);
Reqskill = (bool *(*)(void *)) IL2Cpp::Il2CppGetMethodOffset(OBFUSCATE("Project_d.dll"), OBFUSCATE("Assets.Scripts.GameLogic"), OBFUSCATE("SkillSlot") , OBFUSCATE("RequestUseSkill"), 0);
Reqskill2 = (bool (*)(void *)) IL2Cpp::Il2CppGetMethodOffset(OBFUSCATE("Project_d.dll"), OBFUSCATE("Assets.Scripts.GameLogic"), OBFUSCATE("SkillSlot") , OBFUSCATE("ReadyUseSkill"), 1);

// Removed Hud3d hook - using simple ESP-based detection instead

// Auto Trừng Trị Hook
Tools::Hook((void *) (uintptr_t)IL2Cpp::Il2CppGetMethodOffset(OBFUSCATE("Project_d.dll"), OBFUSCATE("Kyrios.VAGE"), OBFUSCATE("PunishPromptDuration") , OBFUSCATE("get_isHpUnderPunishValue"), 0), (void *) Punish, (void **) &_Punish);

Camera_WorldToScreenPoint = (Vector3 (*)(void *, Vector3))IL2Cpp::Il2CppGetMethodOffset(OBFUSCATE("UnityEngine.CoreModule.dll"), OBFUSCATE("UnityEngine"), OBFUSCATE("Camera") , OBFUSCATE("WorldToScreenPoint"), 1);
Camera_get_main = (void* (*)(void *))IL2Cpp::Il2CppGetMethodOffset(OBFUSCATE("UnityEngine.CoreModule.dll"), OBFUSCATE("UnityEngine"), OBFUSCATE("Camera") , OBFUSCATE("get_main"), 0);
ActorLinker_getPosition = (Vector3 (*)(void *)) IL2Cpp::Il2CppGetMethodOffset(OBFUSCATE("Project_d.dll"), OBFUSCATE("Kyrios.Actor"), OBFUSCATE("ActorLinker") , OBFUSCATE("get_position"), 0);
ActorLinker_ActorTypeDef = (int (*)(void *)) IL2Cpp::Il2CppGetMethodOffset(OBFUSCATE("Project_d.dll"), OBFUSCATE("Kyrios.Actor"), OBFUSCATE("ActorLinker") , OBFUSCATE("get_objType"), 0);


Tools::Hook((void *) (uintptr_t)IL2Cpp::Il2CppGetMethodOffset(OBFUSCATE("Project.Plugins_d.dll"), OBFUSCATE("NucleusDrive.Logic"), OBFUSCATE("LActorRoot") , OBFUSCATE("get_gameObject"), 0), (void *) Wupdate2, (void **) &_Wupdate2);
 
  //Mod skin //
  
  
Tools::Hook((void *) (uintptr_t)IL2Cpp::Il2CppGetMethodOffset(OBFUSCATE("AovTdr.dll"), OBFUSCATE("CSProtocol"), OBFUSCATE("COMDT_HERO_COMMON_INFO") , OBFUSCATE("unpack"), 2), (void *) unpack , (void **) &_unpack);

 Tools::Hook((void *) (uintptr_t)IL2Cpp::Il2CppGetMethodOffset(OBFUSCATE("Project_d.dll"), OBFUSCATE("Assets.Scripts.GameSystem"), OBFUSCATE("CRoleInfo") , OBFUSCATE("GetHeroWearSkinId"), 1), (void *) WearSkinId , (void **) &_WearSkinId);

 Tools::Hook((void *) (uintptr_t)IL2Cpp::Il2CppGetMethodOffset(OBFUSCATE("Project_d.dll"), OBFUSCATE("Assets.Scripts.GameSystem"), OBFUSCATE("CRoleInfo") , OBFUSCATE("IsCanUseSkin"), 2), (void *) IsCanUseSkin , (void **) &_IsCanUseSkin);

Tools::Hook((void *) (uintptr_t)IL2Cpp::Il2CppGetMethodOffset(OBFUSCATE("Project_d.dll"), OBFUSCATE("Assets.Scripts.GameSystem"), OBFUSCATE("CRoleInfo") , OBFUSCATE("IsHaveHeroSkin"), 3), (void *) IsHaveHeroSkin , (void **) &_IsHaveHeroSkin);

Tools::Hook((void *) (uintptr_t)IL2Cpp::Il2CppGetMethodOffset(OBFUSCATE("Project_d.dll"), OBFUSCATE("Assets.Scripts.GameSystem"), OBFUSCATE("HeroSelectNormalWindow") , OBFUSCATE("OnClickSelectHeroSkin"), 2), (void *) Setskin , (void **) &_Setskin);

 _RefreshHeroPanel = (void *(*)(void *,bool,bool,bool))IL2Cpp::Il2CppGetMethodOffset(OBFUSCATE("Project_d.dll"), OBFUSCATE("Assets.Scripts.GameSystem"), OBFUSCATE("HeroSelectNormalWindow") , OBFUSCATE("RefreshHeroPanel"), 3);

 
   
    return NULL;
}



jobjectArray GetFeatureList(JNIEnv *env, jobject context) {
    if (!isAuthenticated() || !CheckModVersion()) return NULL;
    jobjectArray ret;

    const char *features[] = {
        OBFUSCATE("tab1_2_Toggle_Icon Mini Map"),
        OBFUSCATE("tab1_3_Toggle_Vẽ Hộp"),    
        OBFUSCATE("tab1_4_Toggle_ICon Map Lớn"),     
        OBFUSCATE("tab1_5_Toggle_Đường kẻ"),               
        OBFUSCATE("tab1_Category_Điều Chỉnh Icon Mini Map Nếu Lệch"),
        OBFUSCATE("tab1_7_SeekBar_Size Icon Mini Map_8_40"),
        OBFUSCATE("tab1_11_SeekBar_Chỉnh lệch Icon_100_900"),
        OBFUSCATE("tab1_Category_Chú ý : Thông số tham khảo : Size Icon : 19  . Lệch Icon : 410 . Cấu hình trong game để độ phân giải SIÊU CAO ."),
        
        OBFUSCATE("tab2_1_SeekBar_Cam Xa_0_50"),   
        OBFUSCATE("tab3_12_Toggle_Kích hoạt AIM Skill ELSU"),
        OBFUSCATE("tab3_13_RadioButton_Mục Tiêu AIM_Máu thấp nhất,%Máu thấp nhất,Khoảng cách gần nhất,Gần Tia Nhất"),
        OBFUSCATE("tab3_14_Toggle_Bỏ qua kẻ địch tàng hình"),

        // Enhanced Aim System Configuration
        OBFUSCATE("tab3_Category_ Cài Đặt AIM Nâng Cao"),
        OBFUSCATE("tab3_15_Toggle_Sử dụng dữ liệu ESP cho AIM"),
        OBFUSCATE("tab3_16_Toggle_Dự đoán chuyển động kẻ địch"),
        OBFUSCATE("tab3_17_SeekBar_Độ mượt AIM_1_20"),
        OBFUSCATE("tab3_18_Toggle_Hiển thị đường line đến Target"),
        OBFUSCATE("tab3_19_SeekBar_Độ dày đường line_10_50"),

        OBFUSCATE("tab4_6_Toggle_Mod Full Skin"),   
        // Auto System (from LGL GOC - simple and efficient)
        OBFUSCATE("tab4_33_Toggle_Hiện hồi chiêu"),
        OBFUSCATE("tab4_100_Toggle_Kích Hoạt Tự Động Dùng Băng Sương Theo % Máu"),
        OBFUSCATE("tab4_101_SeekBar_% Máu Sẽ Dùng Băng Sương_20_50"),
        OBFUSCATE("tab4_102_Toggle_Auto Dùng Bộc Phá"),
        OBFUSCATE("tab4_103_Toggle_Auto Dùng Trừng Trị"),
     //   OBFUSCATE("tab4_22_Toggle_Dump IL2CPP"),  
     
     
        
        // Tab 5: Tùy Chỉnh
        OBFUSCATE("tab5_Category_Cài Đặt App"),
        OBFUSCATE("tab5_-100_CheckBox_Lưu Cài Đặt Cho Lần Sau_True")
        /*
        OBFUSCATE("9_SeekBar_Map Offset X_-100_100"),
        OBFUSCATE("10_SeekBar_Map Offset Y_-100_100"),
       */
        
           };
    //Now you dont have to manually update the number everytime;
    int Total_Feature = (sizeof features / sizeof features[0]);
    ret = (jobjectArray)
            env->NewObjectArray(Total_Feature, env->FindClass(OBFUSCATE("java/lang/String")),
                                env->NewStringUTF(""));

    for (int i = 0; i < Total_Feature; i++)
        env->SetObjectArrayElement(ret, i, env->NewStringUTF(features[i]));

    return (ret);
}


jobjectArray GetCategoryList(JNIEnv *env, jobject /* context */) {
    // Bỏ kiểm tra bValid
    // if (!bValid || !CheckModVersion()) return NULL;
    
    const char *categories[] = {
        OBFUSCATE("Mini Map"),
        OBFUSCATE("Camera"),
        OBFUSCATE("AIM Skill"),
        OBFUSCATE("Khác"),
        OBFUSCATE("Tùy Chỉnh")
    };
    
    int size = sizeof(categories) / sizeof(categories[0]);
    
    jobjectArray ret = (jobjectArray)env->NewObjectArray(size, 
                                    env->FindClass(OBFUSCATE("java/lang/String")),
                                    env->NewStringUTF(""));
    
    for (int i = 0; i < size; i++) {
        env->SetObjectArrayElement(ret, i, env->NewStringUTF(categories[i]));
    }
    
    return ret;
}



void Changes(JNIEnv *env, jclass clazz, jobject obj, jint featNum, jstring featName, jint value, jboolean boolean, jstring str) {
    if (!isAuthenticated() || !CheckModVersion()) {
        return;
    }

    switch (featNum) {
        case 1:
             Config.WideView.SetFieldOfView = (float) value * 0.0362f;
            Config.WideView.Active = true;
            break;
        
        case 2: 
            Config.ESPMenu.MinimapIcon = boolean;
            Config.ESPMenu.Enable_ESP = boolean;
            break;
        case 3:     
            Config.ESPMenu.PlayerBox = boolean;           
            break;
        case 4:    
            Config.ESPMenu.Icon = boolean;      
            break;      
        case 5:      
            Config.ESPMenu.PlayerLine = boolean;           
            break;      
         
         case 6:
                unlockskin = boolean;
                break;   
               
        case 100:
            Ksbs = boolean;  // Auto Băng Sương
            break;
         case 101:
            Pthp = value;    // % Máu Băng Sương
            break;
       case 102:
           Ksbp = boolean;  // Auto Bộc Phá
           break;
       case 103:
           Kstt = boolean;  // Auto Trừng Trị          
           break;
    
                   
        case 7:
            Config.ESPMenu.IconSize = value;
            break;           
            case 9:
            Config.ESPMenu.MapOffsetX = value - 50; // Để có thể điều chỉnh âm/dương
            break;
        case 10: 
            Config.ESPMenu.MapOffsetY = value - 50; // Để có thể điều chỉnh âm/dương
            break;
        case 11:
            Config.ESPMenu.MapSize = value;
            Config.ESPMenu.BigMapSize = value;
            break;

        case 12:
            AimElsu = boolean;
            break;

        case 13:
             switch (value) {
             case 1:
                aimType = 1;  // Máu thấp nhất
                break;
             case 2:
                aimType = 0;  // % Máu thấp nhất
                break;
             case 3:
                aimType = 2;  // Khoảng cách gần nhất
                break;
				case 4:
                aimType = 3;  // Gần Tia Nhất
                break;
             }
             break;

        case 14:
            aimIgnoreInvisible = boolean;  // Bỏ qua kẻ địch tàng hình
            Config.FEATMenu.aimIgnoreInvisible = boolean;
            break;

        // Enhanced Aim System Configuration Cases
        case 15:
            aimUseESPData = boolean;  // Sử dụng dữ liệu ESP cho aim
            Config.FEATMenu.aimUseESPData = boolean;
            break;

        case 16:
            aimPredictMovement = boolean;  // Dự đoán chuyển động kẻ địch
            Config.FEATMenu.aimPredictMovement = boolean;
            break;

        case 17:
            aimSmoothness = (float)value / 10.0f;  // Độ mượt aim (0.1-2.0)
            if (aimSmoothness < 0.1f) aimSmoothness = 0.1f;
            if (aimSmoothness > 2.0f) aimSmoothness = 2.0f;
            Config.FEATMenu.aimSmoothness = aimSmoothness;
            break;

        case 18:
            aimShowTargetLine = boolean;  // Hiển thị đường line đến target
            Config.FEATMenu.aimShowTargetLine = boolean;
            break;

        case 19:
            aimLineThickness = (float)value / 10.0f;  // Độ dày đường line (1.0-5.0)
            if (aimLineThickness < 1.0f) aimLineThickness = 1.0f;
            if (aimLineThickness > 5.0f) aimLineThickness = 5.0f;
            Config.FEATMenu.aimLineThickness = aimLineThickness;
            break;

        case -100:
            // Auto Save Settings - handled in Java side
            // This case is just for logging/debugging
            break;


            case 33:
          Config.ESPMenu.ShowCd = boolean;
            break;




            case 22:
                
    if (boolean) {
        if (!isDumping) {
            isDumping = true;
            
            std::thread([env]() {
                void* il2cpp_handle = dlopen("libil2cpp.so", RTLD_LAZY);
                if (il2cpp_handle) {
                    il2cpp_dump(il2cpp_handle);
                    dlclose(il2cpp_handle);
                    
                }
                
                isDumping = false;
            }).detach();
        }
    }
    break;

    }
}

__attribute__((constructor))
void lib_main() {
  
}


int RegisterMenu(JNIEnv *env) {
    JNINativeMethod methods[] = {
            {OBFUSCATE("Icon"), OBFUSCATE("()Ljava/lang/String;"), reinterpret_cast<void *>(Icon)},
            {OBFUSCATE("IconWebViewData"),  OBFUSCATE("()Ljava/lang/String;"), reinterpret_cast<void *>(IconWebViewData)},
            {OBFUSCATE("IsGameLibLoaded"),  OBFUSCATE("()Z"), reinterpret_cast<void *>(isGameLibLoaded)},
            {OBFUSCATE("Init"),  OBFUSCATE("(Landroid/content/Context;Landroid/widget/TextView;Landroid/widget/TextView;)V"), reinterpret_cast<void *>(Init)},
            {OBFUSCATE("SettingsList"),  OBFUSCATE("()[Ljava/lang/String;"), reinterpret_cast<void *>(SettingsList)},
            {OBFUSCATE("GetCategoryList"), OBFUSCATE("()[Ljava/lang/String;"), reinterpret_cast<void *>(GetCategoryList)},
    };

    jclass clazz = env->FindClass(OBFUSCATE("com/hmod/vip/Menu"));
    if (!clazz)
        return JNI_ERR;
    if (env->RegisterNatives(clazz, methods, sizeof(methods) / sizeof(methods[0])) != 0)
        return JNI_ERR;
    return JNI_OK;
}


int RegisterFt(JNIEnv *env) {
     if (!isAuthenticated()) return JNI_ERR;
    pthread_t ptid;
    pthread_create(&ptid, NULL, hack_thread, NULL);
    JNINativeMethod methods[] = {
         {OBFUSCATE("GetFeatureList"),  OBFUSCATE("()[Ljava/lang/String;"), reinterpret_cast<void *>(GetFeatureList)},
    };

    jclass clazz = env->FindClass(OBFUSCATE("com/hmod/vip/Menu"));
    if (!clazz)
        return JNI_ERR;
    if (env->RegisterNatives(clazz, methods, sizeof(methods) / sizeof(methods[0])) != 0)
        return JNI_ERR;
    return JNI_OK;
}



int RegisterPreferences(JNIEnv *env) {
    JNINativeMethod methods[] = {
            {OBFUSCATE("Changes"), OBFUSCATE("(Landroid/content/Context;ILjava/lang/String;IZLjava/lang/String;)V"), reinterpret_cast<void *>(Changes)},
    };
    jclass clazz = env->FindClass(OBFUSCATE("com/hmod/vip/Preferences"));
    if (!clazz)
        return JNI_ERR;
    if (env->RegisterNatives(clazz, methods, sizeof(methods) / sizeof(methods[0])) != 0)
        return JNI_ERR;
    return JNI_OK;
}

int RegisterMain(JNIEnv *env) {
    JNINativeMethod methods[] = {
            {OBFUSCATE("CheckOverlayPermission"), OBFUSCATE("(Landroid/content/Context;)V"), reinterpret_cast<void *>(CheckOverlayPermission)},
    };
    jclass clazz = env->FindClass(OBFUSCATE("com/hmod/vip/Main"));
    if (!clazz)
        return JNI_ERR;
    if (env->RegisterNatives(clazz, methods, sizeof(methods) / sizeof(methods[0])) != 0)
        return JNI_ERR;

    return JNI_OK;
}

std::string jstringToString(JNIEnv* env, jstring jstr) {
    if (!jstr) return "";
    
    const char* strChars = env->GetStringUTFChars(jstr, NULL);
    if (!strChars) return "";
    
    std::string str(strChars);
    env->ReleaseStringUTFChars(jstr, strChars);
    return str;
}

extern "C"

JNIEXPORT jstring JNICALL
Java_com_hmod_vip_Menu_Check(JNIEnv *env, jclass clazz, jobject mContext, jstring mUserKey) {
   if(loginAttempts >= MAX_LOGIN_ATTEMPTS) {
       return env->NewStringUTF(OBFUSCATE("Quá nhiều lần đăng nhập sai. Vui lòng đóng trò chơi và mở lại để có thể đăng nhập."));
   }
   
   // Kiểm tra tham số đầu vào
   if (mUserKey == nullptr || mContext == nullptr) {
       return env->NewStringUTF(OBFUSCATE("Lỗi: Tham số đầu vào không hợp lệ"));
   }
   
   // Lấy thông tin signature và MD5 để gửi lên server
   std::string appSignature = checkSignature(env, mContext);
   std::string apkMd5 = checkBaseApkMD5(env, mContext);
   
   // Đặt flag mặc định về false
   secValue2 = secValue1; // Đảm bảo xác thực không hợp lệ
   g_Token.clear();
   g_Auth.clear();
   
   const char* userKey = nullptr;
   try {
       userKey = env->GetStringUTFChars(mUserKey, 0);
       if (!userKey) {
           return env->NewStringUTF(OBFUSCATE("Lỗi khi đọc key"));
       }
   } catch(...) {
       return env->NewStringUTF(OBFUSCATE("Lỗi ngoại lệ khi đọc key"));
   }
   
   // Lấy thông tin cơ bản về thiết bị
   jclass buildClass = nullptr;
   jclass buildVersionClass = nullptr;
   jstring jModel = nullptr;
   jstring jManufacturer = nullptr;
   jstring jRelease = nullptr;
   jstring jFingerprint = nullptr;
   const char* modelStr = nullptr;
   const char* manufacturerStr = nullptr;
   const char* releaseStr = nullptr;
   const char* fingerprintStr = nullptr;
   std::string deviceInfo = "Unknown Device";
   
   try {
       buildClass = env->FindClass(OBFUSCATE("android/os/Build"));
       if (!buildClass) {
           if (userKey) env->ReleaseStringUTFChars(mUserKey, userKey);
           return env->NewStringUTF(OBFUSCATE("Lỗi khi lấy thông tin thiết bị"));
       }
       
       buildVersionClass = env->FindClass(OBFUSCATE("android/os/Build$VERSION"));
       if (!buildVersionClass) {
           env->DeleteLocalRef(buildClass);
           if (userKey) env->ReleaseStringUTFChars(mUserKey, userKey);
           return env->NewStringUTF(OBFUSCATE("Lỗi khi lấy thông tin phiên bản Android"));
       }

       // Lấy tên model thiết bị
       jfieldID modelId = env->GetStaticFieldID(buildClass, OBFUSCATE("MODEL"), OBFUSCATE("Ljava/lang/String;"));
       if (modelId) {
           jModel = (jstring)env->GetStaticObjectField(buildClass, modelId);
           if (jModel) {
               modelStr = env->GetStringUTFChars(jModel, NULL);
           }
       }

       // Lấy tên nhà sản xuất
       jfieldID manufacturerId = env->GetStaticFieldID(buildClass, OBFUSCATE("MANUFACTURER"), OBFUSCATE("Ljava/lang/String;"));
       if (manufacturerId) {
           jManufacturer = (jstring)env->GetStaticObjectField(buildClass, manufacturerId);
           if (jManufacturer) {
               manufacturerStr = env->GetStringUTFChars(jManufacturer, NULL);
           }
       }

       // Lấy phiên bản Android
       jfieldID releaseId = env->GetStaticFieldID(buildVersionClass, OBFUSCATE("RELEASE"), OBFUSCATE("Ljava/lang/String;"));
       if (releaseId) {
           jRelease = (jstring)env->GetStaticObjectField(buildVersionClass, releaseId);
           if (jRelease) {
               releaseStr = env->GetStringUTFChars(jRelease, NULL);
           }
       }

       // Lấy phiên bản Build (Firmware/Software version)
       jfieldID fingerprintId = env->GetStaticFieldID(buildClass, OBFUSCATE("FINGERPRINT"), OBFUSCATE("Ljava/lang/String;"));
       if (fingerprintId) {
           jFingerprint = (jstring)env->GetStaticObjectField(buildClass, fingerprintId);
           if (jFingerprint) {
               fingerprintStr = env->GetStringUTFChars(jFingerprint, NULL);
           }
       }

       // Tạo chuỗi thông tin thiết bị đầy đủ
       char deviceInfoBuffer[1024] = {0};
       snprintf(deviceInfoBuffer, sizeof(deviceInfoBuffer) - 1, "%s %s (Android %s, Build: %s)",
               manufacturerStr ? manufacturerStr : "Unknown",
               modelStr ? modelStr : "Device",
               releaseStr ? releaseStr : "Unknown",
               fingerprintStr ? fingerprintStr : "Unknown");

       deviceInfo = std::string(deviceInfoBuffer);
   } catch (...) {
       // Ignore errors, use default device info
   }
   
   // Tạo hwid từ thông tin người dùng và thiết bị
   std::string hwid;
   std::string UUID;
   
   try {
       hwid = userKey;
       hwid += Tools::GetAndroidID(env, mContext);
   hwid += Tools::GetDeviceModel(env);
   hwid += Tools::GetDeviceBrand(env);

       // Tạo UUID từ hwid
       UUID = Tools::GetDeviceUniqueIdentifier(env, hwid.c_str());
       
       if (UUID.empty()) {
           if (userKey) env->ReleaseStringUTFChars(mUserKey, userKey);
           
           // Giải phóng tài nguyên
           if (modelStr && jModel) env->ReleaseStringUTFChars(jModel, modelStr);
           if (manufacturerStr && jManufacturer) env->ReleaseStringUTFChars(jManufacturer, manufacturerStr);
           if (releaseStr && jRelease) env->ReleaseStringUTFChars(jRelease, releaseStr);
           if (fingerprintStr && jFingerprint) env->ReleaseStringUTFChars(jFingerprint, fingerprintStr);
           
           env->DeleteLocalRef(buildVersionClass);
           env->DeleteLocalRef(buildClass);
           
           return env->NewStringUTF(OBFUSCATE("Không thể tạo ID thiết bị"));
       }
   } catch (...) {
       if (userKey) env->ReleaseStringUTFChars(mUserKey, userKey);
       
       // Giải phóng tài nguyên
       if (modelStr && jModel) env->ReleaseStringUTFChars(jModel, modelStr);
       if (manufacturerStr && jManufacturer) env->ReleaseStringUTFChars(jManufacturer, manufacturerStr);
       if (releaseStr && jRelease) env->ReleaseStringUTFChars(jRelease, releaseStr);
       if (fingerprintStr && jFingerprint) env->ReleaseStringUTFChars(jFingerprint, fingerprintStr);
       
       env->DeleteLocalRef(buildVersionClass);
       env->DeleteLocalRef(buildClass);
       
       return env->NewStringUTF(OBFUSCATE("Lỗi khi tạo ID thiết bị"));
   }
   
   // Giải phóng tài nguyên của thông tin thiết bị
   if (modelStr && jModel) env->ReleaseStringUTFChars(jModel, modelStr);
   if (manufacturerStr && jManufacturer) env->ReleaseStringUTFChars(jManufacturer, manufacturerStr);
   if (releaseStr && jRelease) env->ReleaseStringUTFChars(jRelease, releaseStr);
   if (fingerprintStr && jFingerprint) env->ReleaseStringUTFChars(jFingerprint, fingerprintStr);
   
   env->DeleteLocalRef(buildVersionClass);
   env->DeleteLocalRef(buildClass);
   
   std::string errMsg = "Lỗi kết nối server";
   std::string out;

   struct MemoryStruct chunk{};
   chunk.memory = (char *) malloc(1);
   if (!chunk.memory) {
       if (userKey) env->ReleaseStringUTFChars(mUserKey, userKey);
       return env->NewStringUTF(OBFUSCATE("Lỗi cấp phát bộ nhớ"));
   }
   chunk.size = 0;

   // Đảm bảo cleanup memory trong mọi trường hợp
   auto cleanup_memory = [&]() {
       if (chunk.memory) {
           free(chunk.memory);
           chunk.memory = nullptr;
       }
   };

   CURL *curl = curl_easy_init();
   if (curl) {
       curl_easy_setopt(curl, CURLOPT_CUSTOMREQUEST, "POST");
       curl_easy_setopt(curl, CURLOPT_URL, std::string(OBFUSCATE("https://hmod.io.vn/public/login-app.php")).c_str());
       curl_easy_setopt(curl, CURLOPT_FOLLOWLOCATION, 1L);
       curl_easy_setopt(curl, CURLOPT_DEFAULT_PROTOCOL, "https");
/*
       // Thêm timeout để tránh hang
       curl_easy_setopt(curl, CURLOPT_TIMEOUT, 30L);
       curl_easy_setopt(curl, CURLOPT_CONNECTTIMEOUT, 10L);
       curl_easy_setopt(curl, CURLOPT_LOW_SPEED_LIMIT, 1024L);
       curl_easy_setopt(curl, CURLOPT_LOW_SPEED_TIME, 10L);
       
 */      
       struct curl_slist *headers = NULL;
       headers = curl_slist_append(headers, "Content-Type: application/x-www-form-urlencoded");
       curl_easy_setopt(curl, CURLOPT_HTTPHEADER, headers);

       char data[4096] = {0};
       snprintf(data, sizeof(data) - 1, 
           OBFUSCATE("game=lqm&key=%s&deviceid=%s&version=%s&gamecode=%s&device_info=%s&mod_description=%s&apk_md5=%s&signature=%s"), 
           userKey, 
           UUID.c_str(), 
           MOD_VERSION, 
           GAME_CODE, 
           deviceInfo.c_str(), 
           PhienBan, 
           apkMd5.c_str(), 
           appSignature.c_str());
       
       curl_easy_setopt(curl, CURLOPT_POSTFIELDS, data);
       curl_easy_setopt(curl, CURLOPT_WRITEFUNCTION, WriteMemoryCallback);
       curl_easy_setopt(curl, CURLOPT_WRITEDATA, (void *) &chunk);
       curl_easy_setopt(curl, CURLOPT_SSL_VERIFYPEER, 0L);
       curl_easy_setopt(curl, CURLOPT_SSL_VERIFYHOST, 0L);

       CURLcode res = curl_easy_perform(curl);
       if (res == CURLE_OK) {
           if (chunk.memory && chunk.size > 0) {
               // Đảm bảo chuỗi kết thúc bằng NULL
               if (chunk.memory[chunk.size - 1] != '\0') {
                   char *ptr = (char*)realloc(chunk.memory, chunk.size + 1);
                   if (ptr) {
                       chunk.memory = ptr;
                       chunk.memory[chunk.size] = '\0';
                   } else {
                       if (userKey) env->ReleaseStringUTFChars(mUserKey, userKey);
                       free(chunk.memory);
                       curl_slist_free_all(headers);
                       curl_easy_cleanup(curl);
                       return env->NewStringUTF(OBFUSCATE("Lỗi cấp phát bộ nhớ khi xử lý phản hồi"));
                   }
               }
               
               try {
                   json result = json::parse(chunk.memory);
                   if (result.contains("bool") && result["bool"] == "true") {
                       if (result.contains("token") && result["token"].is_string()) {
                           std::string token = result["token"].get<std::string>();
                           std::string auth = "lqm-" + std::string(userKey) + "-" + UUID + "-Vm8Lk7Uj2JmsjCPVPVjrLa7zgfx3uz9E";
                           std::string outputAuth = CalcMD5(auth);
                           
                           g_Token = token;
                           g_Auth = outputAuth;
                           
                           if(g_Token == g_Auth) {
                               if(!CheckModVersion()) {
                                   // Đảm bảo xác thực không hợp lệ
                                   secValue2 = secValue1;
                                   cleanup_memory();
                                   if (userKey) env->ReleaseStringUTFChars(mUserKey, userKey);
                                   curl_slist_free_all(headers);
                                   curl_easy_cleanup(curl);
                                   return env->NewStringUTF(OBFUSCATE("Phiên bản mod không trùng khớp với dữ liệu trên server. Vui lòng cập nhật!"));
                               }
                               
                               // Thiết lập trạng thái xác thực hợp lệ
                               secValue2 = secValue1 ^ 0x12345678;
                               
                               loginAttempts = 0;
                               out = StrEnc("T#y?xVp?a~`Jhj{>]7", "\x2F\x01\x0B\x5A\x0B\x74\x4A\x1D\x2E\x35\x42\x66\x4A\x02\x1F\x1C\x67\x15", 18).c_str();
                               
                               json responseObj;
                               responseObj["res"] = "OK";
                               
                               if (result.contains("user") && result["user"].is_string()) {
                                   responseObj["user"] = result["user"].get<std::string>();
                               }
                               
                               std::string userInfo;
                               if (result.contains("o") && result["o"].is_object() && 
                                   result["o"].contains("hsd") && result["o"]["hsd"].is_string()) {
                                   userInfo = "HSD : " + result["o"]["hsd"].get<std::string>();
                               } else {
                                   userInfo = "HSD : Unknown";
                               }
                               
                               std::string encodedInfo = base64_encode(userInfo);
                               responseObj["hd"] = encodedInfo;
                               
                               if (encodedInfo.length() >= 10) {
                                   // Chuyển đổi JSON object thành chuỗi để trả về
                                   out = responseObj.dump();
                                   RegisterFt(env);
                               } else {
                                   // Đảm bảo xác thực không hợp lệ
                                   secValue2 = secValue1;
                                   free(chunk.memory);
                                   if (userKey) env->ReleaseStringUTFChars(mUserKey, userKey);
                                   curl_slist_free_all(headers);
                                   curl_easy_cleanup(curl);
                                   return env->NewStringUTF(OBFUSCATE("Có vấn đề về KEY của bạn, vui lòng liên hệ Admin để biết thêm thông tin!"));
                               }
                           } else {
                               // Token không hợp lệ, đảm bảo xác thực không hợp lệ
                               secValue2 = secValue1;
                           }
                       } else {
                           secValue2 = secValue1; // Đảm bảo xác thực không hợp lệ
                           errMsg = "Không nhận được token từ server";
                       }
                   } else if (result.contains("mes") && result["mes"].is_string()) {
                       loginAttempts++;
                       errMsg = result["mes"].get<std::string>();
                   } else {
                       loginAttempts++;
                       errMsg = "Phản hồi không hợp lệ từ server";
                   }
               } catch (const json::parse_error& e) {
                   loginAttempts++;
                   errMsg = "Lỗi xử lý dữ liệu: Không phải định dạng JSON hợp lệ";
               } catch (std::exception &e) {
                   loginAttempts++;
                   errMsg = "Lỗi xử lý dữ liệu";
               } catch (...) {
                   loginAttempts++;
                   errMsg = "Lỗi không xác định khi xử lý dữ liệu";
               }
           } else {
               loginAttempts++;
               errMsg = "Không nhận được dữ liệu từ server";
           }
       } else {
           loginAttempts++;
           errMsg = curl_easy_strerror(res);
       }

       curl_slist_free_all(headers);
       curl_easy_cleanup(curl);
   } else {
       loginAttempts++;
       errMsg = "Không thể khởi tạo kết nối";
   }

   // Cleanup cuối cùng
   cleanup_memory();

   if (userKey) {
       env->ReleaseStringUTFChars(mUserKey, userKey);
   }

   return isAuthenticated() ? env->NewStringUTF(out.c_str()) : env->NewStringUTF(errMsg.c_str());
}

JNIEXPORT jint JNICALL
JNI_OnLoad(JavaVM *vm, void *reserved) {
    loginAttempts = 0;
    
    // Khởi tạo giá trị bảo mật
    srand(time(NULL));
    secValue1 = 0xA5A5A5A5 ^ (rand() & 0xFFFF); // Tạo giá trị cơ sở ngẫu nhiên
    secValue2 = secValue1; // Đảm bảo xác thực không hợp lệ lúc khởi động
    
    g_Token.clear();
    g_Auth.clear();
    
    JNIEnv *env;
    if (vm->GetEnv((void **) &env, JNI_VERSION_1_6) != JNI_OK) {
        return JNI_ERR;
    }
    
    if (RegisterMenu(env) != 0) return JNI_ERR;
    if (RegisterPreferences(env) != 0) return JNI_ERR;
    if (RegisterMain(env) != 0) return JNI_ERR;

    // Log that auto bypass is active
    LOGI(OBFUSCATE("🛡️ ESP Vietnam v3.2 loaded with Auto AntiCheat Bypass"));
    LOGI(OBFUSCATE("📡 Monitoring for Tencent AnoSDK..."));

    return JNI_VERSION_1_6;
}

// ===== ANTICHEAT BYPASS IMPLEMENTATION =====

// Static member definitions
std::atomic<bool> AntiCheatBypass::initialized{false};
std::atomic<bool> AntiCheatBypass::bypass_enabled{true};
std::atomic<int> AntiCheatBypass::call_counter{0};
std::atomic<bool> AntiCheatBypass::should_stop_monitoring{false};
void* AntiCheatBypass::libanogs_base = nullptr;
std::mutex AntiCheatBypass::init_mutex;
std::thread AntiCheatBypass::monitor_thread;

// Original function pointers
int (*AntiCheatBypass::orig_AnoSDKOnRecvSignature)(unsigned char*, long, unsigned int, int) = nullptr;
long (*AntiCheatBypass::orig_loc_1424A8)(long, long, long, long, long) = nullptr;
int (*AntiCheatBypass::orig_sub_14B058)(unsigned char*, long, unsigned int, int) = nullptr;

bool AntiCheatBypass::Initialize() {
    std::lock_guard<std::mutex> lock(init_mutex);

    if (initialized.load()) {
        LOGI(OBFUSCATE("AntiCheat bypass already initialized"));
        return true;
    }

    LOGI(OBFUSCATE("🔧 Initializing AntiCheat bypass..."));

    // Wait for libanogs.so to be loaded
    if (!waitForLibrary(OBFUSCATE("libanogs.so"), 30)) {
        LOGE(OBFUSCATE("❌ libanogs.so not found after waiting"));
        return false;
    }

    libanogs_base = findLibraryBase(OBFUSCATE("libanogs.so"));
    if (!libanogs_base) {
        LOGE(OBFUSCATE("❌ Failed to get libanogs.so base address"));
        return false;
    }

    LOGI(OBFUSCATE("✅ Found libanogs.so at: %p"), libanogs_base);

    // Debug: Log calculated addresses
    uintptr_t test_addr1 = (uintptr_t)libanogs_base + 0x158084;
    uintptr_t test_addr2 = (uintptr_t)libanogs_base + 0x1424A8;
    LOGI(OBFUSCATE("🔍 Calculated AnoSDKOnRecvSignature address: %p"), (void*)test_addr1);
    LOGI(OBFUSCATE("🔍 Calculated loc_1424A8 address: %p"), (void*)test_addr2);

    // Hook functions
    bool hook_success = true;

    if (!HookAnoSDKFunctions()) {
        LOGE(OBFUSCATE("❌ Failed to hook AnoSDK functions"));
        hook_success = false;
    }

    if (!HookCoreVerification()) {
        LOGE(OBFUSCATE("❌ Failed to hook core verification"));
        hook_success = false;
    }

    if (hook_success) {
        initialized.store(true);
        bypass_enabled.store(true);
        obfuscateHookData();
        LOGI(OBFUSCATE("✅ AntiCheat bypass initialized successfully"));
        return true;
    } else {
        LOGE(OBFUSCATE("❌ AntiCheat bypass initialization failed"));
        Cleanup();
        return false;
    }
}

void AntiCheatBypass::Cleanup() {
    if (!initialized.load()) return;

    LOGI(OBFUSCATE("🧹 Cleaning up AntiCheat bypass..."));

    // Stop monitoring thread
    should_stop_monitoring.store(true);
    if (monitor_thread.joinable()) {
        monitor_thread.join();
    }

    // Reset static variables
    initialized.store(false);
    libanogs_base = nullptr;
    bypass_enabled.store(false);
    call_counter.store(0);

    // Reset function pointers
    orig_AnoSDKOnRecvSignature = nullptr;
    orig_loc_1424A8 = nullptr;
    orig_sub_14B058 = nullptr;

    LOGI(OBFUSCATE("✅ AntiCheat bypass cleanup completed"));
}

bool AntiCheatBypass::HookAnoSDKFunctions() {
    if (!libanogs_base) {
        LOGE(OBFUSCATE("libanogs_base is null"));
        return false;
    }

    // Hook AnoSDKOnRecvSignature
    uintptr_t AnoSDKOnRecvSignature_addr = (uintptr_t)libanogs_base + 0x158084;

    if (!isValidAddress((void*)AnoSDKOnRecvSignature_addr)) {
        LOGE(OBFUSCATE("Invalid AnoSDKOnRecvSignature address: %p"), (void*)AnoSDKOnRecvSignature_addr);
        return false;
    }

    if (!verifyFunctionSignature((void*)AnoSDKOnRecvSignature_addr, "")) {
        LOGW(OBFUSCATE("AnoSDKOnRecvSignature signature verification failed, but continuing..."));
        // Continue anyway as signature might have changed
    }

    A64HookFunction(
        (void*)AnoSDKOnRecvSignature_addr,
        (void*)AnoSDKOnRecvSignature_Hook,
        (void**)&orig_AnoSDKOnRecvSignature
    );

    if (orig_AnoSDKOnRecvSignature != nullptr) {
        LOGI(OBFUSCATE("✅ AnoSDKOnRecvSignature hooked successfully at %p"), (void*)AnoSDKOnRecvSignature_addr);
    } else {
        LOGE(OBFUSCATE("❌ Failed to hook AnoSDKOnRecvSignature"));
        return false;
    }

    // Hook sub_14B058 (signature verification function)
    uintptr_t sub_14B058_addr = (uintptr_t)libanogs_base + 0x14B058;

    if (isValidAddress((void*)sub_14B058_addr)) {
        A64HookFunction(
            (void*)sub_14B058_addr,
            (void*)sub_14B058_Hook,
            (void**)&orig_sub_14B058
        );

        if (orig_sub_14B058 != nullptr) {
            LOGI(OBFUSCATE("✅ sub_14B058 hooked successfully at %p"), (void*)sub_14B058_addr);
        } else {
            LOGW(OBFUSCATE("⚠️ Failed to hook sub_14B058"));
        }
    }

    return true;
}

bool AntiCheatBypass::HookCoreVerification() {
    if (!libanogs_base) {
        LOGE(OBFUSCATE("libanogs_base is null"));
        return false;
    }

    // Hook loc_1424A8 (core verification function)
    uintptr_t loc_1424A8_addr = (uintptr_t)libanogs_base + 0x1424A8;

    if (!isValidAddress((void*)loc_1424A8_addr)) {
        LOGE(OBFUSCATE("Invalid loc_1424A8 address: %p"), (void*)loc_1424A8_addr);
        return false;
    }

    if (!verifyFunctionSignature((void*)loc_1424A8_addr, "")) {
        LOGW(OBFUSCATE("loc_1424A8 signature verification failed, but continuing..."));
        // Continue anyway as signature might have changed
    }

    A64HookFunction(
        (void*)loc_1424A8_addr,
        (void*)loc_1424A8_Hook,
        (void**)&orig_loc_1424A8
    );

    if (orig_loc_1424A8 != nullptr) {
        LOGI(OBFUSCATE("✅ loc_1424A8 hooked successfully at %p"), (void*)loc_1424A8_addr);
        return true;
    } else {
        LOGE(OBFUSCATE("❌ Failed to hook loc_1424A8"));
        return false;
    }
}

void AntiCheatBypass::StartAutoBypass() {
    LOGI(OBFUSCATE("🚀 Starting automatic AntiCheat bypass..."));

    // Start monitoring thread
    monitor_thread = std::thread(monitorLibraryLoading);

    LOGI(OBFUSCATE("✅ Auto bypass monitoring started"));
}

// Hook implementations
int AntiCheatBypass::AnoSDKOnRecvSignature_Hook(unsigned char* a1, long a2, unsigned int a3, int a4) {
    int current_call = call_counter.fetch_add(1) + 1;

    if (!bypass_enabled.load() || !shouldBypass()) {
        // Call original function occasionally to avoid detection
        if (orig_AnoSDKOnRecvSignature) {
            return orig_AnoSDKOnRecvSignature(a1, a2, a3, a4);
        }
    }

    LOGI(OBFUSCATE("🔓 AnoSDKOnRecvSignature bypassed (call #%d)"), current_call);

    // Analyze signature type and return appropriate success value
    if (a4 == 1) {
        LOGI(OBFUSCATE("✅ Bypassing APK signature verification"));
    } else if (a4 == 2) {
        LOGI(OBFUSCATE("✅ Bypassing certificate signature verification"));
    } else {
        LOGI(OBFUSCATE("✅ Bypassing signature verification type: %d"), a4);
    }

    return 0; // Success value for signature verification
}

long AntiCheatBypass::loc_1424A8_Hook(long a1, long a2, long a3, long a4, long a5) {
    if (!bypass_enabled.load()) {
        if (orig_loc_1424A8) {
            return orig_loc_1424A8(a1, a2, a3, a4, a5);
        }
        return 1;
    }

    // Analyze call type and return appropriate value
    switch(a1) {
        case 1:   // Basic initialization
            LOGI(OBFUSCATE("✅ Core verification bypassed: initialization"));
            return 1;

        case 10:  // Certificate validation
            LOGI(OBFUSCATE("✅ Core verification bypassed: certificate"));
            return 1;

        case 18:  // APK integrity check
            LOGI(OBFUSCATE("✅ Core verification bypassed: APK integrity"));
            return 1;

        case 28:  // Trust verification
        case 34:  // Signature verification
        case 37:  // Hash verification
        case 39:  // Certificate chain
        case 45:  // APK signature
        case 49:  // Final validation
        case 59:  // Security check
        case 60:  // Final security
            LOGI(OBFUSCATE("✅ Core verification bypassed: type %ld"), a1);
            return 1;

        case 97:  // Critical security check
            LOGI(OBFUSCATE("✅ Critical security check bypassed"));
            return 1;

        default:
            // For unknown calls, log and return success
            LOGI(OBFUSCATE("✅ Unknown verification call bypassed: %ld"), a1);
            return 1;
    }
}

int AntiCheatBypass::sub_14B058_Hook(unsigned char* a1, long a2, unsigned int a3, int a4) {
    if (!bypass_enabled.load() || !shouldBypass()) {
        if (orig_sub_14B058) {
            return orig_sub_14B058(a1, a2, a3, a4);
        }
    }

    LOGI(OBFUSCATE("✅ sub_14B058 signature verification bypassed"));
    return 0; // Success
}

// Helper functions
bool AntiCheatBypass::shouldBypass() {
    // Implement smart bypass logic to avoid detection
    static std::random_device rd;
    static std::mt19937 gen(rd());
    static std::uniform_int_distribution<> dis(1, 100);

    // Bypass 90% of the time, let 10% go through original
    return dis(gen) <= 90;
}

void* AntiCheatBypass::findLibraryBase(const char* library_name) {
    // Method 1: Parse /proc/self/maps to get real base address
    FILE* maps = fopen("/proc/self/maps", "r");
    if (maps) {
        char line[512];
        while (fgets(line, sizeof(line), maps)) {
            if (strstr(line, library_name)) {
                uintptr_t base_addr;
                char perms[8];
                if (sscanf(line, "%lx-%*lx %7s", &base_addr, perms) == 2) {
                    // Only use executable segments
                    if (strchr(perms, 'x')) {
                        fclose(maps);
                        LOGI(OBFUSCATE("Found %s real base address: %p"), library_name, (void*)base_addr);
                        return (void*)base_addr;
                    }
                }
            }
        }
        fclose(maps);
    }

    // Method 2: Fallback - try to find already loaded library
    void* handle = dlopen(library_name, RTLD_NOW | RTLD_NOLOAD);
    if (handle) {
        LOGI(OBFUSCATE("Found already loaded library: %s (handle: %p)"), library_name, handle);
        // Note: This is a handle, not base address - should use Method 1
        return handle;
    }

    // Method 3: Try to load library
    handle = dlopen(library_name, RTLD_NOW);
    if (handle) {
        LOGI(OBFUSCATE("Successfully loaded library: %s"), library_name);
        return handle;
    }

    LOGE(OBFUSCATE("Failed to find library: %s"), library_name);
    return nullptr;
}

bool AntiCheatBypass::isValidAddress(void* addr) {
    if (!addr) {
        LOGW(OBFUSCATE("Address is null"));
        return false;
    }

    uintptr_t address = (uintptr_t)addr;

    // Check if address is in valid user space range for ARM64
    if (address < 0x8000 || address >= 0x800000000000ULL) {
        LOGW(OBFUSCATE("Address out of valid range: %p"), addr);
        return false;
    }

    // Check if address is in a mapped region by parsing /proc/self/maps
    FILE* maps = fopen("/proc/self/maps", "r");
    if (maps) {
        char line[512];
        while (fgets(line, sizeof(line), maps)) {
            uintptr_t start, end;
            char perms[8];
            if (sscanf(line, "%lx-%lx %7s", &start, &end, perms) == 3) {
                if (address >= start && address < end) {
                    fclose(maps);
                    // Check if it's readable and executable
                    if (strchr(perms, 'r') && strchr(perms, 'x')) {
                        LOGI(OBFUSCATE("Address %p is valid (in mapped region %lx-%lx %s)"), addr, start, end, perms);
                        return true;
                    } else {
                        LOGW(OBFUSCATE("Address %p is in mapped region but not executable (%s)"), addr, perms);
                        return false;
                    }
                }
            }
        }
        fclose(maps);
    }

    LOGW(OBFUSCATE("Address %p is not in any mapped region"), addr);
    return false;
}

bool AntiCheatBypass::verifyFunctionSignature(void* addr, const char* expected_pattern) {
    if (!isValidAddress(addr)) {
        return false;
    }

    // Simple verification - check if the first few bytes match expected pattern
    // This is a basic check to ensure we're hooking the right function
    try {
        unsigned char* bytes = (unsigned char*)addr;

        // For ARM64, check for common function prologue patterns
        // Most functions start with: stp x29, x30, [sp, #-0x??]! (0xa9bf7bfd or similar)
        uint32_t first_instruction = *(uint32_t*)bytes;

        // Check for ARM64 function prologue patterns
        if ((first_instruction & 0xffc003ff) == 0xa9800000 ||  // stp pattern
            (first_instruction & 0xffc003ff) == 0xa9b00000 ||  // stp pattern (variant)
            (first_instruction & 0xff000000) == 0xd1000000 ||  // sub sp pattern
            (first_instruction & 0xff000000) == 0xf9000000) {  // str pattern

            LOGI(OBFUSCATE("Function signature verified at %p (instruction: 0x%08x)"), addr, first_instruction);
            return true;
        }

        // For now, accept any valid ARM64 instruction and just warn
        LOGW(OBFUSCATE("Function signature mismatch at %p (instruction: 0x%08x), but continuing..."), addr, first_instruction);
        return true; // Continue anyway since address is valid

    } catch (...) {
        LOGE(OBFUSCATE("Failed to read function signature at %p"), addr);
        return false;
    }
}

void AntiCheatBypass::obfuscateHookData() {
    // Simple obfuscation to hide hook presence in memory
    static volatile int dummy_data[16] = {0};
    int current_counter = call_counter.load();
    for (int i = 0; i < 16; i++) {
        dummy_data[i] = current_counter ^ (i * 0x12345678);
    }
}

void AntiCheatBypass::monitorLibraryLoading() {
    LOGI(OBFUSCATE("📡 Starting library monitoring thread..."));

    while (!should_stop_monitoring.load()) {
        if (!initialized.load()) {
            // Check if libanogs.so is loaded
            void* lib_handle = dlopen(OBFUSCATE("libanogs.so"), RTLD_NOW | RTLD_NOLOAD);
            if (lib_handle) {
                dlclose(lib_handle);

                LOGI(OBFUSCATE("🎯 libanogs.so detected! Initializing bypass..."));

                if (Initialize()) {
                    LOGI(OBFUSCATE("🛡️ Auto bypass successfully activated!"));
                    break; // Exit monitoring loop
                } else {
                    LOGW(OBFUSCATE("⚠️ Auto bypass initialization failed, retrying..."));
                }
            }
        }

        // Sleep for 3 seconds before next check (reduced spam)
        std::this_thread::sleep_for(std::chrono::seconds(3));
    }

    LOGI(OBFUSCATE("📡 Library monitoring thread stopped"));
}

bool AntiCheatBypass::waitForLibrary(const char* library_name, int max_wait_seconds) {
    LOGI(OBFUSCATE("⏳ Waiting for %s (max %d seconds)..."), library_name, max_wait_seconds);

    for (int i = 0; i < max_wait_seconds; i++) {
        void* handle = dlopen(library_name, RTLD_NOW | RTLD_NOLOAD);
        if (handle) {
            dlclose(handle);
            LOGI(OBFUSCATE("✅ %s found after %d seconds"), library_name, i);
            return true;
        }

        std::this_thread::sleep_for(std::chrono::seconds(1));
    }

    LOGE(OBFUSCATE("❌ %s not found after %d seconds"), library_name, max_wait_seconds);
    return false;
}

// Auto-initialization class implementation
AutoAntiCheatBypass::AutoAntiCheatBypass() {
    LOGI(OBFUSCATE("🚀 AutoAntiCheatBypass constructor called"));

    // Start auto bypass in a separate thread to avoid blocking
    std::thread([]() {
        // Small delay to ensure everything is loaded
        std::this_thread::sleep_for(std::chrono::milliseconds(100));
        AntiCheatBypass::StartAutoBypass();
    }).detach();
}

AutoAntiCheatBypass::~AutoAntiCheatBypass() {
    LOGI(OBFUSCATE("🧹 AutoAntiCheatBypass destructor called"));
    AntiCheatBypass::Cleanup();
}

// Global instance for auto-initialization
AutoAntiCheatBypass g_auto_bypass;















