<?php
// Thiết lập header JSON và cấu hình cơ bản
header('Content-Type: application/json; charset=utf-8');
error_reporting(0);

if (session_status() == PHP_SESSION_NONE) session_start();
include_once("config/config.php");

// === UTILITY FUNCTIONS ===
function safeErrorLog($message) {
    $logDir = 'logs';
    if (!is_dir($logDir)) @mkdir($logDir, 0755, true);
    error_log(date('[Y-m-d H:i:s] ') . $message . PHP_EOL, 3, $logDir . '/error_' . date('Y-m-d') . '.log');
}

function return_bytes($val) {
    $val = trim($val);
    $multiplier = ['k' => 1024, 'm' => 1024*1024, 'g' => 1024*1024*1024];
    $last = strtolower(substr($val, -1));
    return (int)$val * ($multiplier[$last] ?? 1);
}

function IsSafe($string) {
    return preg_match('/[^a-zA-Z0-9_-]/', $string) == 0;
}

function generateToken($key, $deviceid) {
    return md5("lqm-{$key}-{$deviceid}-Vm8Lk7Uj2JmsjCPVPVjrLa7zgfx3uz9E");
}

function sendErrorResponse($message) {
    exit(json_encode(["bool" => "false", "mes" => $message]));
}

function parseDeviceInfo($device_info) {
    $result = ['model' => $device_info, 'android' => "Unknown", 'build' => "Unknown"];
    if (preg_match('/(.+?) \(Android (.+?), Build: (.+?)\)/', $device_info, $matches)) {
        $result = ['model' => $matches[1], 'android' => $matches[2], 'build' => $matches[3]];
    }
    return $result;
}

function processKeyStatus($con, $keyData, $key) {
    $status = $keyData["trangthai"];

    if ($status == "hoatdong") {
        if (strtotime($keyData["hansudung"]) < time()) {
            sendErrorResponse("Key đã hết hạn sử dụng!");
        }
    } elseif ($status == "dangcho") {
        // Kích hoạt key mới
        $date = date("Y-m-d H:i:s");
        $datetime = new DateTime($date);
        $datetime->modify('+'.$keyData["hansudung"].' day');
        $newExpiry = $datetime->format('Y-m-d H:i:s');

        $stmt = $con->prepare("UPDATE keytab SET trangthai='hoatdong', ngaykichhoat=?, hansudung=? WHERE tenkey=?");
        $stmt->bind_param("sss", $date, $newExpiry, $key);
        $stmt->execute();
        $stmt->close();

        $keyData["hansudung"] = $newExpiry;
        $keyData["ngaykichhoat"] = $date;
    } else {
        sendErrorResponse("Key đã bị khóa!");
    }

    return $keyData;
}

function logLoginHistory($con, $input, $keyData, $deviceInfo) {
    try {
        // Tạo bảng nếu chưa tồn tại
        $con->query("CREATE TABLE IF NOT EXISTS login_history (
            id INT AUTO_INCREMENT PRIMARY KEY,
            key_id VARCHAR(255) NOT NULL,
            key_value VARCHAR(255) NOT NULL,
            device_info TEXT,
            device_model VARCHAR(255),
            android_version VARCHAR(50),
            build_version VARCHAR(50),
            game_code VARCHAR(50),
            mod_version VARCHAR(50),
            mod_title VARCHAR(255),
            user_ip VARCHAR(45),
            login_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )");

        // Lấy mod title
        $modTitle = "";
        if (!empty($input['version'])) {
            $stmt = $con->prepare("SELECT title FROM version_config WHERE version = ? AND game_code = ? LIMIT 1");
            $stmt->bind_param("ss", $input['version'], $input['gamecode']);
            $stmt->execute();
            $result = $stmt->get_result();
            if ($result && $result->num_rows > 0) {
                $row = $result->fetch_assoc();
                $modTitle = $row['title'];
            }
            $stmt->close();
        }

        // Insert log
        $stmt = $con->prepare("INSERT INTO login_history
            (key_id, key_value, device_info, device_model, android_version, build_version,
             game_code, mod_version, mod_title, user_ip)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");

        $keyId = $keyData['id'] ?? $input['key'];
        $ipAddress = $_SERVER['REMOTE_ADDR'];

        $stmt->bind_param("ssssssssss",
            $keyId, $input['key'], $input['device_info'],
            $deviceInfo['model'], $deviceInfo['android'], $deviceInfo['build'],
            $input['gamecode'], $input['version'], $modTitle, $ipAddress
        );

        $stmt->execute();
        $stmt->close();
    } catch (Exception $e) {
        safeErrorLog("Lỗi ghi login_history: " . $e->getMessage());
    }
}

function sendDiscordNotification($input, $keyData, $deviceInfo, $sellerName) {
    if (!function_exists('curl_init')) return;

    try {
        $botToken = "MTM3NjQxODYyMjk2MDA0NjE1MQ.G-PCBY.f3AbUmBP54j5NzRiOYD1bUfSShdjLCh0zXm3Z4";
        $channelIds = ["739326247154483234"];

        $embed = [
            "title" => "🔐 Thông báo đăng nhập",
            "color" => 3447003,
            "timestamp" => date('c'),
            "fields" => [
                ["name" => "📌 Key", "value" => "`{$input['key']}`", "inline" => true],
                ["name" => "👤 Seller", "value" => $sellerName, "inline" => true],
                ["name" => "⏰ Thời gian", "value" => date("Y-m-d H:i:s"), "inline" => true],
                ["name" => "📱 Thông tin thiết bị", "value" => "**Model:** {$deviceInfo['model']}\n**Android:** {$deviceInfo['android']}\n**Build:** {$deviceInfo['build']}", "inline" => false],
                ["name" => "🔑 Thông tin KEY", "value" => "**Kích hoạt:** {$keyData['ngaykichhoat']}\n**Hạn sử dụng:** {$keyData['hansudung']}\n**Game Code:** {$input['gamecode']}", "inline" => false],
                ["name" => "🔖 Phiên bản", "value" => $input['version'], "inline" => true],
                ["name" => "📝 Loại MOD", "value" => $input['mod_description'], "inline" => true]
            ],
            "footer" => ["text" => "H-MOD Login System", "icon_url" => "https://cdn.discordapp.com/embed/avatars/1.png"]
        ];

        foreach ($channelIds as $channelId) {
            $ch = curl_init();
            curl_setopt_array($ch, [
                CURLOPT_URL => "https://discord.com/api/v10/channels/{$channelId}/messages",
                CURLOPT_POST => 1,
                CURLOPT_POSTFIELDS => json_encode(["embeds" => [$embed]]),
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_SSL_VERIFYPEER => false,
                CURLOPT_TIMEOUT => 10,
                CURLOPT_CONNECTTIMEOUT => 3,
                CURLOPT_HTTPHEADER => [
                    'Content-Type: application/json',
                    'Authorization: Bot ' . $botToken
                ]
            ]);
            @curl_exec($ch);
            curl_close($ch);
        }
    } catch (Exception $e) {
        safeErrorLog("Lỗi Discord notification: " . $e->getMessage());
    }
}

// === VERSION CHECK FUNCTION ===
function checkVersion($con, $version, $apk_md5, $signature, $game_code) {
    if (empty($version) || empty($game_code)) return 4;

    // Kiểm tra và tạo bảng nếu cần
    $tables = ['version_config', 'mod_app_mapping'];
    foreach ($tables as $table) {
        $result = $con->query("SHOW TABLES LIKE '{$table}'");
        if ($result->num_rows == 0 && $table == 'mod_app_mapping') {
            $con->query("CREATE TABLE `mod_app_mapping` (
                `id` int(11) NOT NULL AUTO_INCREMENT,
                `version_config_id` int(11) NOT NULL,
                `app_identifier` varchar(100) NOT NULL,
                PRIMARY KEY (`id`),
                UNIQUE KEY `version_app` (`version_config_id`, `app_identifier`),
                CONSTRAINT `fk_version_config` FOREIGN KEY (`version_config_id`) REFERENCES `version_config` (`id`) ON DELETE CASCADE
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;");
        }
    }

    $stmt = $con->prepare("SELECT * FROM version_config WHERE version = ? AND game_code = ?");
    $stmt->bind_param("ss", $version, $game_code);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result->num_rows == 0) {
        $stmt->close();
        return 4;
    }

    $row = $result->fetch_assoc();
    $stmt->close();

    if ($row['is_active'] != 1) return 1;
    if (!empty($apk_md5) && $row['apk_md5'] != $apk_md5) return 2;
    if (!empty($signature) && $row['signature'] != $signature) return 3;

    return 0;
}

// === MAIN PROCESSING ===
$resinfo = [];

// Kiểm tra bảo trì
if (isset($_SESSION["baotri"]) && $_SESSION["baotri"] == "yes") {
    sendErrorResponse("Web Site Đang Bảo Trì 🔧");
}

// === LOGIN HANDLER ===
if (isset($_POST["key"])) {
    // Thu thập dữ liệu đầu vào
    $input = [
        'key' => $_POST["key"],
        'deviceid' => $_POST["deviceid"] ?? "",
        'gamecode' => $_POST["gamecode"] ?? "",
        'version' => $_POST["version"] ?? "N/A",
        'apk_md5' => $_POST["apk_md5"] ?? "",
        'signature' => $_POST["signature"] ?? "",
        'mod_description' => $_POST["mod_description"] ?? "N/A",
        'key_id' => $_POST["key_id"] ?? "",
        'device_info' => $_POST["device_info"] ?? "Android"
    ];

    // Phân tích thông tin thiết bị
    $deviceInfo = parseDeviceInfo($input['device_info']);

    // Validation cơ bản
    if (empty($input['deviceid']) || empty($input['gamecode'])) {
        sendErrorResponse("Thiếu thông tin thiết bị hoặc mã game!");
    }

    // Kiểm tra phiên bản
    $versionCheck = checkVersion($con, $input['version'], $input['apk_md5'], $input['signature'], $input['gamecode']);
    if ($versionCheck != 0) {
        $errorMessages = [
            1 => "Phiên bản này đã bị vô hiệu hóa. Vui lòng cập nhật lên phiên bản mới!",
            2 => "Kiểm tra tính toàn vẹn của ứng dụng thất bại. APK có thể đã bị chỉnh sửa!",
            3 => "Chữ ký ứng dụng không hợp lệ. Vui lòng cài đặt ứng dụng từ nguồn chính thức!",
            4 => "Phiên bản này không được hỗ trợ. Vui lòng cập nhật lên phiên bản chính thức!"
        ];
        sendErrorResponse($errorMessages[$versionCheck] ?? "Không thể xác thực phiên bản. Vui lòng liên hệ admin!");
    }

    // Kiểm tra key
    if (strlen($input['key']) < 10) sendErrorResponse("Key không hợp lệ vui lòng kiểm tra lại!");
    if (!IsSafe($input['key'])) sendErrorResponse("Định dạng key không chính xác!");

    // Kiểm tra key trong database
    $stmt = $con->prepare("SELECT * FROM keytab WHERE tenkey = ?");
    $stmt->bind_param("s", $input['key']);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result->num_rows == 0) {
        $stmt->close();
        sendErrorResponse("Key không tồn tại trong hệ thống!");
    }

    $keyData = $result->fetch_assoc();
    $stmt->close();

    if ($keyData["game"] != $input['gamecode']) {
        sendErrorResponse("Key này hiện tại không thể sử dụng với MOD này!");
    }

    // Xử lý trạng thái key
    $keyData = processKeyStatus($con, $keyData, $input['key']);

    // Xử lý thiết bị
    $devices = json_decode($keyData["may"], true);
    $deviceFound = false;

    if (is_array($devices)) {
        foreach ($devices as &$device) {
            if ($device === $input['deviceid'] || $device === "Empty") {
                $device = $input['deviceid'];
                $deviceFound = true;
                break;
            }
        }
    }

    if (!$deviceFound) {
        sendErrorResponse("Đăng nhập quá thiết bị cho phép, liên hệ admin!");
    }

    // Cập nhật thiết bị
    $devicesJson = json_encode($devices);
    $stmt = $con->prepare("UPDATE keytab SET may=? WHERE tenkey=?");
    $stmt->bind_param("ss", $devicesJson, $input['key']);

    if($stmt->execute()) {
        $stmt->close();

        // Cập nhật trạng thái bán
        if($keyData["daban"] == "no") {
            $stmt = $con->prepare("UPDATE admin SET sokeydaban=sokeydaban+1 WHERE code=?");
            $stmt->bind_param("s", $keyData["code"]);
            $stmt->execute();
            $stmt->close();

            $stmt = $con->prepare("UPDATE keytab SET daban='yes' WHERE tenkey=?");
            $stmt->bind_param("s", $input['key']);
            $stmt->execute();
            $stmt->close();
        }

        // Lấy tên người bán - THAY ĐỔI Ở ĐÂY
        $stmt = $con->prepare("SELECT hovaten FROM admin WHERE code = ?");
        $stmt->bind_param("s", $keyData["code"]);
        $stmt->execute();
        $result = $stmt->get_result();
        
        // Kiểm tra xem có tìm thấy seller không
        if ($result->num_rows == 0) {
            $stmt->close();
            sendErrorResponse("Không hỗ trợ !");
        }
        
        $adminInfo = $result->fetch_assoc();
        $stmt->close();
        
        // Kiểm tra xem hovaten có null hoặc rỗng không
        if (empty($adminInfo['hovaten'])) {
            sendErrorResponse("Không hỗ trợ !");
        }
        
        $sellerName = $adminInfo['hovaten'];

        // Tạo response thành công
        $resinfo = [
            "bool" => "true",
            "mes" => "Đăng nhập thành công!",
            "o" => ["key" => $input['key'], "hsd" => $keyData["hansudung"]],
            "token" => generateToken($input['key'], $input['deviceid']),
            "user" => $sellerName
        ];
        $resinfo["in4"] = base64_encode(json_encode($resinfo["o"]));

        // Ghi log và gửi thông báo
        logLoginHistory($con, $input, $keyData, $deviceInfo);

        // Gửi response
        $response = json_encode($resinfo);
        header('Content-Length: ' . strlen($response));
        echo $response;

        if (ob_get_level() > 0) ob_end_flush();
        flush();

        // Gửi Discord notification (không chặn response)
        sendDiscordNotification($input, $keyData, $deviceInfo, $sellerName);

        exit;
    } else {
        $stmt->close();
        safeErrorLog("Lỗi cập nhật thiết bị cho key {$input['key']}: " . $con->error);
        sendErrorResponse("Lỗi cập nhật thông tin thiết bị!");
    }
}

// Đoạn code kiểm tra trạng thái mod (thay thế phần đọc từ JSON)
if(isset($_POST["check_status"])) {
    // Lấy game_code và app_identifier từ request
    $game_code = isset($_POST["gamecode"]) ? $_POST["gamecode"] : "";
    $app_identifier = isset($_POST["app_identifier"]) ? $_POST["app_identifier"] : "";

    // Đảm bảo trả về JSON hợp lệ ngay cả khi có lỗi
    try {
        // Kiểm tra bảng mod_app_mapping có tồn tại không
        $tableExists = false;
        $checkTable = $con->query("SHOW TABLES LIKE 'mod_app_mapping'");
        if ($checkTable && $checkTable->num_rows > 0) {
            $tableExists = true;
        }

        // Lấy danh sách tất cả các MOD có is_active = 1 và game_code phù hợp
        $query = "SELECT id, version, lib_name, safety, safety_message, description, icon, title, status, file_path, vpn_check_enabled
                  FROM version_config
                  WHERE is_active = 1";

        if (!empty($game_code)) {
            $query .= " AND game_code = ?";
            $stmt = $con->prepare($query);
            $stmt->bind_param("s", $game_code);
        } else {
            $stmt = $con->prepare($query);
        }

        $stmt->execute();
        $result = $stmt->get_result();

        // Khởi tạo mảng để lưu thông tin MOD
        $mod_info = array();

        // Nếu có kết quả
        if ($result && $result->num_rows > 0) {
            while ($row = $result->fetch_assoc()) {
                $lib_name = $row['lib_name'];
                $version_id = $row['id'];

                // Bỏ qua nếu lib_name trống
                if (empty($lib_name)) continue;

                // Mặc định hiển thị MOD
                $show_mod = true;

                // Nếu có app_identifier và bảng mod_app_mapping tồn tại
                if (!empty($app_identifier) && $tableExists) {
                    // Kiểm tra xem MOD này có bản ghi nào trong bảng mod_app_mapping không
                    $map_query = "SELECT COUNT(*) as count FROM mod_app_mapping WHERE version_config_id = ?";
                    $map_stmt = $con->prepare($map_query);
                    $map_stmt->bind_param("i", $version_id);
                    $map_stmt->execute();
                    $map_result = $map_stmt->get_result();
                    $map_row = $map_result->fetch_assoc();
                    $has_mappings = ($map_row['count'] > 0);
                    $map_stmt->close();

                    // Nếu MOD có bản ghi trong bảng mod_app_mapping
                    if ($has_mappings) {
                        // Kiểm tra xem app_identifier này có được phép hiển thị MOD này không
                        $app_query = "SELECT COUNT(*) as count FROM mod_app_mapping
                                      WHERE version_config_id = ? AND app_identifier = ?";
                        $app_stmt = $con->prepare($app_query);
                        $app_stmt->bind_param("is", $version_id, $app_identifier);
                        $app_stmt->execute();
                        $app_result = $app_stmt->get_result();
                        $app_row = $app_result->fetch_assoc();
                        $show_mod = ($app_row['count'] > 0);
                        $app_stmt->close();
                    }
                }

                // Nếu MOD được hiển thị
                if ($show_mod) {
                    // Thêm thông tin MOD vào mảng kết quả
                    $mod_info[$lib_name] = array(
                        'version' => $row['version'],
                        'safety' => $row['safety'],
                        'safety_message' => $row['safety_message'],
                        'description' => $row['description'],
                        'icon' => $row['icon'],
                        'title' => $row['title'],
                        'status' => $row['status'],
                        'vpn_check_enabled' => (bool)$row['vpn_check_enabled']
                    );

                    // Kiểm tra file có tồn tại không
                    $file_exists = false;
                    $file_path = empty($row['file_path']) ? "files/" . $lib_name : $row['file_path'];
                    if (file_exists($file_path)) {
                        $file_exists = true;
                    } elseif (file_exists(dirname(__FILE__) . "/" . $file_path)) {
                        $file_exists = true;
                    }

                    $mod_info[$lib_name]['status'] = $file_exists ? 'ready' : 'not_ready';
                }
            }
        }

        $stmt->close();

        // Trả về kết quả
        $response = array(
            "status" => "success",
            "data" => $mod_info
        );

        exit(json_encode($response));
    } catch (Exception $e) {
        // Ghi log lỗi
        error_log("Error in check_status: " . $e->getMessage());

        // Trả về lỗi
        exit(json_encode(array(
            "status" => "error",
            "message" => "Đã xảy ra lỗi, vui lòng thử lại sau.",
            "data" => array() // Đảm bảo luôn có trường data
        )));
    }
}

// Đoạn code xử lý download lib
else if(isset($_POST["libname"])) {
    try {
        $libname = isset($_POST["libname"]) ? $_POST["libname"] : "";
        $game_code = isset($_POST["gamecode"]) ? $_POST["gamecode"] : "";
        $app_identifier = isset($_POST["app_identifier"]) ? $_POST["app_identifier"] : "";

        // Kiểm tra tham số bắt buộc
        if (empty($libname) || empty($game_code)) {
            exit(json_encode(array(
                "status" => "error",
                "message" => "Thiếu thông tin cần thiết!"
            )));
        }

        // Kiểm tra tên file an toàn
        if(!preg_match('/^[a-zA-Z0-9_\-\.]+$/', $libname)) {
            exit(json_encode(array(
                "status" => "error",
                "message" => "Tên file không hợp lệ!"
            )));
        }

        // Kiểm tra bảng mod_app_mapping có tồn tại không
        $tableExists = false;
        $checkTable = $con->query("SHOW TABLES LIKE 'mod_app_mapping'");
        if ($checkTable && $checkTable->num_rows > 0) {
            $tableExists = true;
        }

        // Truy vấn SQL để lấy thông tin MOD với điều kiện game_code
        $stmt = $con->prepare("SELECT id, version, file_path FROM version_config WHERE lib_name = ? AND is_active = 1 AND game_code = ?");
        $stmt->bind_param("ss", $libname, $game_code);
        $stmt->execute();
        $result = $stmt->get_result();

        if (!$result || $result->num_rows == 0) {
            $stmt->close();
            exit(json_encode(array(
                "status" => "error",
                "message" => "Không tìm thấy MOD cho game code: " . $game_code
            )));
        }

        $row = $result->fetch_assoc();
        $version_id = $row['id'];
        $show_mod = true;

        // Nếu có app_identifier và bảng mod_app_mapping tồn tại
        if (!empty($app_identifier) && $tableExists) {
            // Kiểm tra xem MOD này có bản ghi nào trong bảng mod_app_mapping không
            $map_query = "SELECT COUNT(*) as count FROM mod_app_mapping WHERE version_config_id = ?";
            $map_stmt = $con->prepare($map_query);
            $map_stmt->bind_param("i", $version_id);
            $map_stmt->execute();
            $map_result = $map_stmt->get_result();
            $map_row = $map_result->fetch_assoc();
            $has_mappings = ($map_row['count'] > 0);
            $map_stmt->close();

            // Nếu MOD có bản ghi trong bảng mod_app_mapping
            if ($has_mappings) {
                // Kiểm tra xem app_identifier này có được phép hiển thị MOD này không
                $app_query = "SELECT COUNT(*) as count FROM mod_app_mapping
                              WHERE version_config_id = ? AND app_identifier = ?";
                $app_stmt = $con->prepare($app_query);
                $app_stmt->bind_param("is", $version_id, $app_identifier);
                $app_stmt->execute();
                $app_result = $app_stmt->get_result();
                $app_row = $app_result->fetch_assoc();
                $show_mod = ($app_row['count'] > 0);
                $app_stmt->close();
            }
        }

        // Nếu MOD không được hiển thị cho app_identifier này
        if (!$show_mod) {
            $stmt->close();
            exit(json_encode(array(
                "status" => "error",
                "message" => "MOD này không khả dụng cho ứng dụng của bạn."
            )));
        }

        // Lấy thông tin file
        $file_path = empty($row['file_path']) ? "files/" . $libname : $row['file_path'];
        $version = $row['version'];
        $stmt->close();

        // Kiểm tra file có tồn tại không
        $file_content = null;
        $actual_file_path = null;

        if (file_exists($file_path)) {
            $actual_file_path = $file_path;
        } elseif (file_exists(dirname(__FILE__) . "/" . $file_path)) {
            $actual_file_path = dirname(__FILE__) . "/" . $file_path;
        }

        if ($actual_file_path !== null) {
            // Kiểm tra kích thước file trước khi load
            $file_size = filesize($actual_file_path);
            $max_file_size = 50 * 1024 * 1024; // 50MB limit

            if ($file_size > $max_file_size) {
                exit(json_encode(array(
                    "status" => "error",
                    "message" => "File quá lớn để tải xuống!"
                )));
            }

            // Kiểm tra memory available
            $memory_needed = $file_size * 2; // Base64 encoding needs ~1.33x + overhead
            $memory_limit = ini_get('memory_limit');
            $memory_available = return_bytes($memory_limit) - memory_get_usage();

            if ($memory_needed > $memory_available) {
                exit(json_encode(array(
                    "status" => "error",
                    "message" => "Không đủ bộ nhớ để xử lý file!"
                )));
            }

            $file_content = file_get_contents($actual_file_path);

            if ($file_content !== false) {
                $response = array(
                    "status" => "success",
                    "version" => $version,
                    "data" => base64_encode($file_content)
                );
                exit(json_encode($response));
            } else {
                exit(json_encode(array(
                    "status" => "error",
                    "message" => "Không thể đọc file dữ liệu!"
                )));
            }
        } else {
            exit(json_encode(array(
                "status" => "error",
                "message" => "File dữ liệu không tồn tại!"
            )));
        }
    } catch (Exception $e) {
        // Ghi log lỗi
        error_log("Error in download lib: " . $e->getMessage());

        // Trả về lỗi
        exit(json_encode(array(
            "status" => "error",
            "message" => "Đã xảy ra lỗi, vui lòng thử lại sau."
        )));
    }
}

// Xử lý các request không hợp lệ
exit(json_encode(array(
    "status" => "error",
    "message" => "Invalid request"
)));